Create table temp
(col0 int GENERATED ALWAYS AS IDENTITY(START WITH 100 INCREMENT BY 5),
col00 int GENERATED ALWAYS AS IDENTITY(START WITH 100 INCREMENT BY 5),
col1 int,
col2 numeric(20),
col3 numeric(10,2),
col4 numeric,
col5 varchar(20),
col6 varchar(20),
col7 text,
col8 bytea,
col9 UUID default gen_random_uuid(),
col10 varchar(20),
col11 timestamp,
col12 timestamp default LOCALTIMESTAMP,
col13 timestamp(6) default current_timestamp,
col14  boolean default true,
col15  text,
col17 double precision,
col18  bytea ,
col19  text,
col20  char(10),
col21  char(10),
col22  double precision,
col23  double precision
);