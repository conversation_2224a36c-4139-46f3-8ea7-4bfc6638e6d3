# Standard library imports
import os
import pandas as pd
import re
from typing import Dict, Any, List, Optional
import datetime, shutil
import importlib.util

# Local imports - State
from Conversion_Agent_Stage2.state import (
    Stage2WorkflowState,
    ResponsibleFeaturesAnalysisOutput,
    IdentifiedFeaturesValidationOutput,
    ModuleEnhancementOutput,

    StatementComparisonOutput
)
from config import Config
from Conversion_Agent_Stage2.qmigrator_conversion.object_conversion import qbook_object_conversion, decrypt_conversion_file, replace_comment_markers
from Conversion_Agent_Stage2.prompts.responsible_features_identification_prompt import (
    create_responsible_features_identification_prompt
)
from Conversion_Agent_Stage2.prompts.identified_responsible_features_validation_prompt import (
    create_identified_responsible_features_validation_prompt
)

from Conversion_Agent_Stage2.prompts.module_enhancement_prompt import (
    create_module_enhancement_prompt
)

from Conversion_Agent_Stage2.utils.database_names import (
    get_database_specific_terms
)
from Conversion_Agent_Stage2.prompts.ai_statement_comparison_prompt import (
    create_ai_statement_comparison_prompt
)
from Conversion_Agent_Stage2.utils.driver_module_enhancement import DriverModuleEnhancer

def get_stage2_excel_path(metadata_dir: str, schema_name: str, object_name: str) -> str:
    """Get the path for the Stage 2 workflow tracking Excel file."""
    filename = f"{schema_name}_{object_name}_Stage2.xlsx"
    return os.path.join(metadata_dir, filename)

def create_excel_with_sheet(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> str:
    """
    Generic function to create new Excel file with first sheet.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to create
        data: List of dictionaries containing sheet data

    Returns:
        str: Path to the created Excel file
    """
    try:
        df = pd.DataFrame(data)
        with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        return file_path
    except Exception as e:
        print(f"❌ Error creating Excel file {file_path}: {e}")
        return ""

def append_sheet_to_excel(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> bool:
    """
    Generic function to append data to existing Excel sheet without reading full content.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to append
        data: List of dictionaries containing sheet data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        df = pd.DataFrame(data)

        if not os.path.exists(file_path):
            # Create new file
            with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            # Append to existing file - add rows to the bottom of existing sheet
            with pd.ExcelWriter(file_path, mode='a', if_sheet_exists='overlay', engine='openpyxl') as writer:
                # Check if sheet exists to determine start row
                if sheet_name in writer.book.sheetnames:
                    existing_sheet = writer.book[sheet_name]
                    start_row = existing_sheet.max_row  # Start after last row
                    df.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=start_row)
                else:
                    # Sheet doesn't exist, create new with headers
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

        return True
    except Exception as e:
        print(f"❌ Error appending sheet {sheet_name} to {file_path}: {e}")
        return False

class Stage2ProcessingNodes:
    """
    Stage 2 processing nodes for QMigrator module updates.

    This class provides all 15 workflow nodes for the complete Stage 2 processing
    as defined in Stage2_LangGraph_Workflow.md. Handles both qmigrator (object-level)
    and qbook (statement-level) processing with comprehensive retry mechanisms.

    Complete Workflow Nodes:
        1. Process Type Decision
        2. Post Stage1 Processing (QMigrator)
        3. Statement Level Processing (QBook)
        4. Map Feature Combinations (QMigrator only)
        5. Available Features Validation (QMigrator only)
        6. Identify Responsible Features
        7. Features Valid Decision
        8. Update Responsible Modules

        10. Apply Updated Modules
        11. Test Modules
        12. Compare AI Statements
        13. More Statements Decision
        14. Complete Processing

    Workflow Integration:
        Designed for use with LangGraph workflow orchestration, providing seamless
        state management and conditional routing based on validation results.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 processing nodes with AI language model integration.

        Sets up the node collection with the provided language model for AI-driven
        analysis throughout the Stage 2 module update workflow.

        Args:
            llm: Initialized Language Model instance supporting structured outputs
                 for reliable AI analysis and module update operations. Compatible with
                 multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

        Attributes:
            llm: The language model instance used across all Stage 2 nodes
        """
        self.llm = llm

    # ==================== WORKFLOW DECISION NODES ====================

    def process_type_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 1: Process Type Decision Node.

        Routes workflow based on process_type parameter.
        - qmigrator: Routes to QMigrator-specific file processing
        - qbook: Routes to QBook-specific statement processing
        """
        print(f"🔀 Process Type Decision: {state.process_type}")

        if state.process_type == "qmigrator":
            print("📁 Routing to QMigrator object-level processing")
        else:
            print("📝 Routing to QBook statement-level processing")

        # Return state update - routing is handled by conditional edges
        return {
            "process_type": state.process_type
        }

    # ==================== QMIGRATOR PATH NODES ====================

    def post_stage1_processing_qmigrator(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Post-Stage 1 Processing Node (QMigrator Path).

        Purpose: Read Stage 1 approved statements and source code, run QMigrator conversion.

        Process:
            1. Read approved_statements.csv from Stage 1 metadata
            2. Read source_code.sql file
            3. Run QMigrator object-level conversion
            4. Return DataFrames for workflow processing

        Args:
            state: Stage2WorkflowState containing migration context

        Returns:
            Dict containing DataFrames and conversion results
        """
        print("🔄 Starting Post-Stage 1 Processing (QMigrator)...")
        print(f"🔧 Migration: {state.migration_name}")
        print(f"🏗️ Schema: {state.schema_name}, Object: {state.object_name}, Type: {state.objecttype}")

        try:
            # Clean process-type-specific feature modules directory for fresh start when reprocessing
            self.cleanup_process_feature_modules(state)
            # Determine paths based on cloud_category
            if state.cloud_category.lower() == "local":
                qbook_root_path = Config.Qbook_Local_Path
                temp_root_path = Config.Temp_Local_Path
            else:
                qbook_root_path = Config.Qbook_Path
                temp_root_path = Config.Temp_Path

            # Construct metadata directory path
            metadata_dir = os.path.join(
                qbook_root_path,
                'Stage1_Metadata',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )

            # Create temp directory for Stage 2 processing
            temp_dir = os.path.join(
                temp_root_path,
                'Stage2_Processing',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )
            os.makedirs(temp_dir, exist_ok=True)

            approved_statements_path = os.path.join(metadata_dir, 'approved_statements.csv')
            source_code_path = os.path.join(metadata_dir, 'source_code.sql')

            print(f"📁 Metadata directory: {metadata_dir}")

            # 3. Read approved statements CSV
            if not os.path.exists(approved_statements_path):
                raise FileNotFoundError(f"Approved statements file not found: {approved_statements_path}")

            approved_statements_df = pd.read_csv(approved_statements_path)
            print(f"📊 Loaded {len(approved_statements_df)} approved statements")

            # 4. Read source code file
            if not os.path.exists(source_code_path):
                raise FileNotFoundError(f"Source code file not found: {source_code_path}")

            with open(source_code_path, 'r', encoding='utf-8') as f:
                source_code_content = f.read()

            if not source_code_content.strip():
                raise ValueError("Source code file is empty")

            print(f"📝 Loaded source code ({len(source_code_content)} characters)")

            # 5. Run QMigrator object-level conversion
            print("🔄 Running QMigrator object-level conversion...")

            object_converted_output, available_features_df, comment_dict = qbook_object_conversion(
                migration_name=state.migration_name,
                schema_name=state.schema_name,
                object_type=state.objecttype,
                object_name=state.object_name,
                source_data=source_code_content,
                cloud_category=state.cloud_category
            )
            
            # print(object_converted_output, 'object_converted_output=======================')

            if object_converted_output is None:
                raise ValueError("QMigrator conversion failed - no output generated")

            print(f"✅ QMigrator conversion completed")
            print(f"📊 Available features: {len(available_features_df)} statements")

            # Create Stage 2 Excel tracking file in temp directory
            stage2_excel_path = get_stage2_excel_path(temp_dir, state.schema_name, state.object_name)
            final_qbook_excel_path = get_stage2_excel_path(metadata_dir, state.schema_name, state.object_name)
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Prepare data for Excel sheets
            source_code_data = [{
                "Migration_Name": state.migration_name,
                "Schema_Name": state.schema_name,
                "Object_Name": state.object_name,
                "Object_Type": state.objecttype,
                "Source_Code": source_code_content,
                "Timestamp": current_timestamp
            }]

            approved_statements_data = []
            for idx, row in approved_statements_df.iterrows():
                approved_statements_data.append({
                    "Migration_Name": row.get('migration_name', state.migration_name),
                    "Schema_Name": row.get('schema_name', state.schema_name),
                    "Object_Name": row.get('object_name', state.object_name),
                    "Object_Type": row.get('object_type', state.objecttype),
                    "TGT_Object_ID": row.get('tgt_object_id', ''),
                    "Source_Statement_Number": row.get('source_statement_number', ''),
                    "Target_Statement_Number": row.get('target_statement_number', ''),
                    "Original_Source_Statement": str(row.get('original_source_statement', '')),
                    "Original_Target_Statement": str(row.get('original_target_statement', '')),
                    "AI_Converted_Statement": str(row.get('ai_converted_statement', '')),
                    "Original_Deployment_Error": str(row.get('original_deployment_error', '')),
                    "Timestamp": current_timestamp
                })

            available_features_data = available_features_df.copy()
            available_features_data['Timestamp'] = current_timestamp
            available_features_list = available_features_data.to_dict('records')

            # Create Excel file with three sheets
            create_excel_with_sheet(stage2_excel_path, "Source_Code", source_code_data)
            append_sheet_to_excel(stage2_excel_path, "Approved_Statements", approved_statements_data)
            append_sheet_to_excel(stage2_excel_path, "Available_Features", available_features_list)
            print(f"📋 Sheet 'Available_Features' added with {len(available_features_list)} records")

            print("✅ Post-Stage 1 Processing (QMigrator) completed successfully")
            print(f"📊 Total approved statements: {len(approved_statements_df)}")
            print(f"📊 Total available features: {len(available_features_df)}")

            # 7. Return state fields for LangGraph persistence
            return {
                "approved_statements": approved_statements_df.to_dict('records'),
                "object_level_features": available_features_df.to_dict('records'),
                "source_code": source_code_content,
                "stage2_excel_path": stage2_excel_path,
                "final_qbook_excel_path": final_qbook_excel_path,
                "metadata_dir": metadata_dir,
                "temp_dir": temp_dir,
                "approved_statements_df": approved_statements_df,
                "available_features_df": available_features_df,
                "object_converted_output": object_converted_output,
                "comments_dict": comment_dict
            }

        except FileNotFoundError as e:
            error_msg = f"❌ File not found: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements": [],
                "object_level_features": [],
                "source_code": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": "",
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "object_converted_output": "",
                "comments_dict": {}
            }

        except Exception as e:
            error_msg = f"❌ Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "comments_dict": {},
                "source_code": "",
                "object_converted_output": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": ""
            }

    def map_feature_combinations(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 3: Map Feature Combinations Node (QMigrator Only).

        Purpose: Map approved statements with object-level features based on statement number matching.
        Only creates combined entries when statement numbers match between approved statements and available features.

        Process:
            1. Get approved statements and available features from workflow state
            2. Map statements only when source_statement_number matches Statement_Number
            3. Create combined dataset for feature analysis with matching entries only
            4. Create Excel sheet with combined data

        Args:
            state: Stage2WorkflowState containing approved_statements and object_level_features

        Returns:
            Dict containing available_features_with_statements and excel_path
        """
        print("🔄 Starting Map Feature Combinations...")

        try:
            # Get DataFrames from workflow state
            if not state.approved_statements or not state.object_level_features:
                raise ValueError("Missing approved statements or object level features from previous step")

            # Convert state data back to DataFrames for processing
            approved_statements_df = pd.DataFrame(state.approved_statements)
            available_features_df = pd.DataFrame(state.object_level_features)

            print(f"📊 Processing {len(approved_statements_df)} approved statements")
            print(f"📊 Processing {len(available_features_df)} available features")

            # Create combined dataset only for matching statement numbers
            combined_features = []
            statement_id_counter = 1

            for _, approved_stmt in approved_statements_df.iterrows():
                # Find matching features by statement number
                source_stmt_num = approved_stmt.get('source_statement_number', 0)
                matching_features = available_features_df[
                    available_features_df['Statement_Number'] == source_stmt_num
                ]

                # Only create combined entry if matching features exist
                if not matching_features.empty:
                    feature_row = matching_features.iloc[0]
                    combined_entry = {
                        # Combined dataset ID
                        "statement_id": statement_id_counter,

                        # From approved statements
                        "migration_name": approved_stmt.get('migration_name', ''),
                        "schema_name": approved_stmt.get('schema_name', ''),
                        "object_name": approved_stmt.get('object_name', ''),
                        "object_type": approved_stmt.get('object_type', ''),
                        "tgt_object_id": approved_stmt.get('tgt_object_id', 0),
                        "source_statement_number": approved_stmt.get('source_statement_number', 0),
                        "target_statement_number": approved_stmt.get('target_statement_number', 0),
                        "original_source_statement": approved_stmt.get('original_source_statement', ''),
                        "original_target_statement": approved_stmt.get('original_target_statement', ''),
                        "ai_converted_statement": approved_stmt.get('ai_converted_statement', ''),
                        "original_deployment_error": approved_stmt.get('original_deployment_error', ''),

                        # From available features
                        "statement_after_typecasting": feature_row.get('Statement_After_Typecasting', ''),
                        "statement_level_output": feature_row.get('Statement_Level_Output', ''),
                        "available_features": feature_row.get('Available_Features', []),
                        "post_features": feature_row.get('Post_Features', [])
                    }
                    combined_features.append(combined_entry)
                    statement_id_counter += 1
                else:
                    print(f"⚠️ No matching features found for approved statement {source_stmt_num} - excluding from combined dataset")

            # Add Combined_Features sheet to existing Excel file using existing function
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                # Prepare combined data with timestamp
                combined_data = []
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                if combined_features:
                    for feature in combined_features:
                        combined_record = feature.copy()
                        combined_record['Timestamp'] = current_timestamp
                        combined_data.append(combined_record)
                else:
                    # Add empty record if no combined features
                    combined_data = [{"Message": "No matching features found", "Timestamp": current_timestamp}]

                # Use existing append_sheet_to_excel function
                append_sheet_to_excel(state.stage2_excel_path, "Combined_Features", combined_data)
                print(f"📋 Sheet 'Combined_Features' added with {len(combined_data)} records")
            else:
                print("⚠️ Stage 2 Excel file path not found in state - cannot add Combined_Features sheet")

            print("✅ Map Feature Combinations completed successfully")
            print(f"📊 Total matched combinations: {len(combined_features)}")

            return {
                "available_features_with_statements": combined_features
            }

        except Exception as e:
            error_msg = f"❌ Map Feature Combinations failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "available_features_with_statements": []
            }
    # ==================== QBOOK PATH NODES ====================

    def statement_level_processing_qbook(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Statement Level Processing Node (QBook Path).

        Purpose: Process individual statements using QBook statement-level conversion.

        Process:
            Run QMigrator statement-level conversion on single statement
            Return converted statement for workflow processing

        Args:
            state: Stage2WorkflowState containing statement context

        Returns:
            Dict containing converted statement results
        """
        print("🔄 Starting Statement Level Processing (QBook)...")

        try:
            # TODO: Implement QBook statement-level processing
            # Get individual statement from state
            # Run QBook conversion for single statement
            # Return converted statement results

            print("✅ Statement Level Processing (QBook) completed successfully")

            return {
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

        except Exception as e:
            error_msg = f"❌ Statement Level Processing (QBook) failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

    # ==================== FEATURE IDENTIFICATION NODES ====================

    def identify_responsible_features(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 5: Identify Responsible Features Node.

        Purpose: Identify specific Python modules responsible for conversion issues.

        Process:
            Analyze original_source_statement vs ai_converted_statement vs original_target_statement
            Read and decrypt Python modules from available_features paths
            Use AI to determine which modules caused wrong conversion
            Map keywords from Oracle_Postgres14.csv to identify responsible modules

        Args:
            state: Stage2WorkflowState containing available_features_with_statements

        Returns:
            Dict containing responsible features analysis for each statement
        """
        print("🔄 Starting Identify Responsible Features...")

        try:
            # Get combined data and current statement index from state
            if not state.available_features_with_statements:
                raise ValueError("Missing available_features_with_statements from previous step")

            combined_data = state.available_features_with_statements
            current_statement_index = getattr(state, 'current_statement_index', 0)

            print(f"📊 Processing statement {current_statement_index + 1}/{len(combined_data)}")

            # Get the current statement to process
            if current_statement_index >= len(combined_data):
                raise ValueError(f"Current statement index {current_statement_index} exceeds available statements {len(combined_data)}")

            current_statement_data = combined_data[current_statement_index]
            statement_id = current_statement_data.get('statement_id', current_statement_index + 1)
            source_stmt_num = current_statement_data.get('source_statement_number', 'Unknown')

            print(f"🔍 Processing Current Statement:")
            print(f"   📝 Statement ID: {statement_id}")
            print(f"   📝 Source Statement Number: {source_stmt_num}")
            print(f"   📝 Position: {current_statement_index + 1}/{len(combined_data)}")

            # Extract key statement information for analysis
            original_source = current_statement_data.get('original_source_statement', '')
            ai_converted = current_statement_data.get('ai_converted_statement', '')
            actual_target = current_statement_data.get('original_target_statement', '')
            deployment_error = current_statement_data.get('original_deployment_error', '')

            print(f"🔍 Oracle Source: {original_source[:100]}...")
            print(f"🎯 Expected PostgreSQL: {ai_converted[:100]}...")
            print(f"❌ Actual PostgreSQL: {actual_target[:100]}...")
            if deployment_error:
                print(f"⚠️ Deployment Error: {deployment_error[:100]}...")

            # Get migration name and cloud category from state (Request-First Approach)
            migration_name = state.migration_name
            cloud_category = getattr(state, 'cloud_category', 'cloud')  # Default to 'cloud' if not found
            print(f"🔧 Using dynamic migration name: {migration_name}")
            print(f"☁️ Using cloud category: {cloud_category}")

            # Load keyword mapping dynamically based on migration_name and cloud_category
            keyword_mapping = self.load_keyword_mapping(migration_name, cloud_category)

            # Get module paths from available_features for this statement
            available_features = current_statement_data.get("available_features", [])
            print(f"📦 Available features for analysis: {len(available_features)} modules")

            module_paths = self.get_module_paths(
                available_features,
                current_statement_data.get("migration_name", migration_name),
                cloud_category
            )

            # Decrypt and read Python modules for this statement
            decrypted_modules = self.decrypt_and_read_modules(module_paths)
            print(f"🔓 Decrypted {len(decrypted_modules)} modules for analysis")

            # Get validation feedback if available
            validation_feedback = getattr(state, 'validation_feedback', None)
            if validation_feedback:
                print(f"🔄 Using validation feedback for improved identification...")
                print(f"📝 Feedback: {validation_feedback[:100]}...")

            # AI analysis to identify responsible modules for this specific statement
            print(f"🧠 Starting AI analysis for current statement...")
            analysis_result = self.ai_analyze_responsible_modules(
                current_statement_data, decrypted_modules, keyword_mapping, migration_name, validation_feedback
            )

            # Extract results
            responsible_features = analysis_result.get("responsible_features", [])
            analysis_summary = analysis_result.get("analysis_summary", "No analysis summary available")

            # Log results for this statement
            print(f"✅ Current statement analysis complete:")
            print(f"   📊 Responsible modules found: {len(responsible_features)}")

            if responsible_features:
                module_names = [feature[0] for feature in responsible_features]
                print(f"   🎯 Modules: {', '.join(module_names)}")
            else:
                print("   ✅ No responsible modules - conversion working correctly")

            # Log to Excel (optional - for tracking purposes)
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                # Format responsible features for Excel logging with keywords
                features_for_excel = []
                for feature_data in responsible_features:
                    if len(feature_data) == 3:
                        feature_name, module_path, keywords = feature_data
                        features_for_excel.append(f"{feature_name} | {module_path} | Keywords: {keywords}")
                    else:
                        feature_name, module_path = feature_data
                        features_for_excel.append(f"{feature_name} | {module_path} | Keywords: N/A")

                # Create comprehensive log entry with detailed analysis
                log_entry = {
                    "Statement_ID": statement_id,
                    "Source_Statement_Number": source_stmt_num,
                    "Responsible_Modules_Count": len(responsible_features),
                    "Responsible_Features": features_for_excel,  # Include keywords in readable format
                    "Responsible_Features_Raw": responsible_features,  # Keep original tuples for processing
                    "Analysis": analysis_summary,  # Detailed explanation of analysis process and findings
                    "Processing_Attempt": getattr(state, 'current_attempt', 1),
                    "Timestamp": current_timestamp
                }

                append_sheet_to_excel(state.stage2_excel_path, "Responsible_Features", [log_entry])
                print(f"📋 Logged statement {current_statement_index + 1} analysis")

            print(f"\n✅ Identify Responsible Features completed for statement {current_statement_index + 1}")
            print(f"📊 responsible modules identified: {responsible_features}")

            # Clear validation feedback after use
            result = {
                "responsible_features": responsible_features
            }

            # Clear validation feedback if it was used
            if validation_feedback:
                result["validation_feedback"] = None
                print("🔄 Cleared validation feedback after use")

            return result

        except Exception as e:
            error_msg = f"❌ Identify Responsible Features failed: {str(e)}"
            print(error_msg)
            return {
                "responsible_features": [],  # Return empty list on error
                "error": error_msg
            }

    # ==================== HELPER METHODS ====================



    def load_keyword_mapping(self, migration_name: str, cloud_category: str) -> List[Dict[str, Any]]:
        """
        Load keyword-to-module mapping dynamically based on migration_name.

        Args:
            migration_name: Dynamic migration name from database configuration
            cloud_category: Cloud category from state (local/cloud)

        Returns:
            List of dictionaries containing Feature_Name, Keywords, Object_Path mapping
        """
        try:
            # Get QBook path dynamically based on cloud category from state
            if cloud_category.lower() == 'local':
                qbook_path = Config.Qbook_Local_Path
                print(f"🏠 Using Local QBook path: {qbook_path}")
            else:
                qbook_path = Config.Qbook_Path
                print(f"☁️ Using Cloud QBook path: {qbook_path}")

            # Dynamic CSV path: qbook_path/Conversion_Modules/{migration_name}/{migration_name}.csv
            csv_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, f"{migration_name}.csv")

            if not os.path.exists(csv_path):
                print(f"⚠️ {migration_name}.csv not found at {csv_path}")
                return []

            df = pd.read_csv(csv_path)
            keyword_mapping = df[['Feature_Name', 'Keywords', 'Object_Path']].to_dict('records')

            print(f"📋 Loaded {len(keyword_mapping)} keyword mappings from {csv_path}")
            return keyword_mapping

        except Exception as e:
            print(f"❌ Failed to load keyword mapping: {str(e)}")
            return []

    def get_module_paths(self, available_features: List[tuple], migration_name: str, cloud_category: str) -> List[tuple]:
        """
        Get full paths to Python modules from available_features using dynamic paths.

        Args:
            available_features: List of tuples like [('update_alias', 'Common/Statement/Pre/update_alias.py')]
            migration_name: Dynamic migration name from database configuration
            cloud_category: Cloud category from state (local/cloud)

        Returns:
            List of tuples with (feature_name, full_module_path)
        """
        module_paths = []

        # Get QBook path dynamically based on cloud category from state
        if cloud_category.lower() == 'local':
            qbook_path = Config.Qbook_Local_Path
            print(f"🏠 Using Local QBook path for modules: {qbook_path}")
        else:
            qbook_path = Config.Qbook_Path
            print(f"☁️ Using Cloud QBook path for modules: {qbook_path}")

        for feature_name, relative_path in available_features:
            # Dynamic module path: qbook_path/Conversion_Modules/{migration_name}/{relative_path}
            full_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, relative_path)
            module_paths.append((feature_name, full_path))

        print(f"📁 Resolved {len(module_paths)} module paths for analysis")
        return module_paths

    def decrypt_and_read_modules(self, module_paths: List[tuple]) -> Dict[str, str]:
        """
        Decrypt and read Python modules from given paths using existing decryption logic.

        Args:
            module_paths: List of tuples with (feature_name, module_path)

        Returns:
            Dictionary mapping feature_name to decrypted module content
        """
        decrypted_modules = {}

        # Hardcoded decrypt key from object_conversion.py
        decrypt_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='

        for feature_name, module_path in module_paths:
            try:
                if os.path.exists(module_path):
                    # Use existing decryption function from object_conversion.py
                    decrypted_content = decrypt_conversion_file(module_path, decrypt_key)
                    decrypted_modules[feature_name] = decrypted_content
                    print(f"🔓 Decrypted module: {feature_name}")
                else:
                    print(f"⚠️ Module not found: {module_path}")

            except Exception as e:
                print(f"❌ Failed to decrypt module {feature_name}: {str(e)}")

        print(f"📚 Successfully decrypted {len(decrypted_modules)} modules")
        return decrypted_modules

    def ai_analyze_responsible_modules(self, statement_data: Dict[str, Any],
                                     decrypted_modules: Dict[str, str],
                                     keyword_mapping: List[Dict[str, Any]],
                                     migration_name: str,
                                     validation_feedback: Optional[str] = None) -> Dict[str, Any]:
        """
        Use AI to analyze which modules are responsible for conversion issues.

        Args:
            statement_data: Combined statement data with source, target, and AI converted statements
            decrypted_modules: Dictionary of decrypted Python module contents
            keyword_mapping: Keyword-to-module mapping from Oracle_Postgres14.csv
            migration_name: Migration name for database-specific terms
            validation_feedback: Optional feedback from previous validation attempts

        Returns:
            Dictionary containing responsible features analysis
        """
        try:
            # Extract key information for analysis
            original_source = statement_data.get('original_source_statement', '')
            ai_converted = statement_data.get('ai_converted_statement', '')
            actual_target = statement_data.get('original_target_statement', '')
            deployment_error = statement_data.get('original_deployment_error', '')
            available_features = statement_data.get('available_features', [])

            # Get database-specific terms for prompts (clean names without versions)
            db_terms = get_database_specific_terms(migration_name)

            # Prepare AI analysis prompt using prompts folder with validation feedback
            analysis_prompt = create_responsible_features_identification_prompt(
                original_source, ai_converted, actual_target,
                deployment_error, decrypted_modules, keyword_mapping, available_features,
                validation_feedback=validation_feedback,
                db_terms=db_terms
            )

            # Use structured output for reliable AI analysis
            structured_llm = self.llm.client.with_structured_output(ResponsibleFeaturesAnalysisOutput)
            ai_result = structured_llm.invoke(analysis_prompt)

            # Extract responsible features and analysis summary with complete module paths
            responsible_features = []
            for feature in ai_result.responsible_features:
                original_feature_name = feature.feature_name
                module_path = feature.module_path

                # Fix feature name to match actual Python function name (lowercase)
                # CSV might have "Xml_sequence" but function is "xml_sequence"
                corrected_feature_name = original_feature_name.lower()

                # Complete the module path if it's incomplete (from CSV keyword mapping)
                if module_path.endswith('.py'):
                    # Path is already complete (e.g., from available features)
                    complete_path = module_path
                else:
                    # Path is incomplete (e.g., from CSV keyword mapping like "Common/Pre")
                    # Add the lowercase feature name + .py (using corrected name)
                    complete_path = f"{module_path}/{corrected_feature_name}.py"

                # Find keywords for this feature from keyword mapping
                feature_keywords = ''
                for mapping in keyword_mapping:
                    if mapping.get('Feature_Name', '').lower() == corrected_feature_name.lower():
                        feature_keywords = mapping.get('Keywords', '')
                        break

                # Store feature with keywords as tuple: (feature_name, module_path, keywords)
                responsible_features.append((corrected_feature_name, complete_path, feature_keywords))

                if original_feature_name != corrected_feature_name:
                    print(f"🔧 Corrected feature name: {original_feature_name} → {corrected_feature_name}")
                print(f"🔧 Completed path for {corrected_feature_name}: {complete_path}")
                if feature_keywords:
                    print(f"🔑 Keywords for {corrected_feature_name}: {feature_keywords}")

            analysis_summary = ai_result.analysis_summary

            print(f"🧠 AI identified {len(responsible_features)} responsible modules")

            if responsible_features:
                print("🎯 Responsible modules identified by AI:")
                for feature in ai_result.responsible_features:
                    print(f"   - {feature.feature_name}: {feature.responsibility_reason}")
            else:
                print("✅ No responsible modules identified - conversion working correctly")

            return {
                "responsible_features": responsible_features,
                "analysis_summary": analysis_summary
            }

        except Exception as e:
            print(f"❌ AI analysis failed: {str(e)}")
            return {
                "responsible_features": [],
                "analysis_summary": f"AI analysis failed: {str(e)}. Manual review required."
            }

    def features_valid_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 6: Features Valid Decision Node.

        Purpose: Validate ONLY the already identified responsible features to determine if they are
        truly responsible for the conversion failure. Does NOT re-check available features.

        Business Logic:
        - Takes the specific modules identified by the previous node
        - Validates: "Are these identified modules truly responsible for this conversion failure?"
        - Returns: YES (proceed to update) or NO (go back to re-identify different modules)

        Process:
        1. Get ONLY the identified responsible features from state (not all available features)
        2. Use AI to validate if each identified feature is truly responsible
        3. Log validation results to Excel
        4. Return validation status and feedback for re-identification if needed

        Returns:
            Dict with responsible_features_valid flag and validation_feedback
        """
        print("🔄 Starting Features Valid Decision...")
        print("🎯 Purpose: Validate ONLY the identified responsible features")

        try:
            # Get responsible features from the latest identification
            responsible_features = getattr(state, 'responsible_features', [])

            if not responsible_features:
                print("⚠️ No identified features to validate")
                return {
                    "responsible_features_valid": False,
                    "validation_feedback": "No features were identified as responsible."
                }

            print(f"🔍 Validating {len(responsible_features)} identified responsible features:")
            for i, (feature_name, module_path) in enumerate(responsible_features, 1):
                print(f"   {i}. {feature_name} -> {module_path}")

            # Perform AI validation ONLY on the identified features
            validation_result = self.perform_ai_features_validation(state)

            # Log validation results to Excel
            self.log_features_validation_to_excel(state, validation_result)

            # Return validation results
            if validation_result.get('overall_validation_passed', False):
                print("✅ Features validation passed - all identified features are truly responsible")
                return {
                    "responsible_features_valid": True,
                    "validation_feedback": None  # Clear any previous feedback
                }
            else:
                print("❌ Features validation failed - some identified features are not truly responsible")
                feedback = validation_result.get('feedback_for_reidentification',
                                               "Some identified features are not truly responsible. Please refine identification.")
                print(f"📝 Feedback for re-identification: {feedback[:100]}...")
                return {
                    "responsible_features_valid": False,
                    "validation_feedback": feedback
                }

        except Exception as e:
            print(f"❌ Features validation failed: {str(e)}")
            return {
                "responsible_features_valid": False,
                "validation_feedback": f"Validation failed due to error: {str(e)}"
            }

    def perform_ai_features_validation(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Perform AI validation of identified responsible features.

        Purpose: Validate ONLY the already identified features to determine if they are truly responsible
        for the conversion failure. Does NOT re-check available features.

        Args:
            state: Current workflow state

        Returns:
            Dict containing validation results for identified features only
        """
        try:
            # Get current statement data from state
            combined_data = state.available_features_with_statements
            current_statement_index = getattr(state, 'current_statement_index', 0)
            current_statement_data = combined_data[current_statement_index]

            # Get validation context from current statement
            original_source = current_statement_data.get('original_source_statement', '')
            ai_converted = current_statement_data.get('ai_converted_statement', '')
            actual_target = current_statement_data.get('original_target_statement', '')
            deployment_error = current_statement_data.get('original_deployment_error', '')

            # Get ONLY the identified responsible features from state (not all available features)
            identified_features = getattr(state, 'responsible_features', [])
            validation_feedback = getattr(state, 'validation_feedback', None)

            if not identified_features:
                print("⚠️ No identified features to validate")
                return {
                    "validation_results": [],
                    "overall_validation_passed": False,
                    "validation_summary": "No features were identified for validation",
                    "feedback_for_reidentification": "No features were identified as responsible. Please identify responsible features first."
                }

            # Get migration name and cloud category from state
            migration_name = state.migration_name
            cloud_category = getattr(state, 'cloud_category', 'cloud')

            # Get QBook path for absolute path construction
            if cloud_category.lower() == 'local':
                qbook_path = Config.Qbook_Local_Path
            else:
                qbook_path = Config.Qbook_Path

            # Convert relative paths to absolute paths for decryption
            identified_module_paths = []
            for feature_name, relative_path in identified_features:
                # Construct absolute path: qbook_path/Conversion_Modules/{migration_name}/{relative_path}
                absolute_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, relative_path)
                identified_module_paths.append((feature_name, absolute_path))
                print(f"🔧 Absolute path for {feature_name}: {absolute_path}")

            # Decrypt ONLY the identified modules with absolute paths
            decrypted_modules = self.decrypt_and_read_modules(identified_module_paths)

            print(f"🤖 Performing AI validation for {len(identified_features)} identified features...")
            print(f"🔍 Features to validate: {[f[0] for f in identified_features]}")

            # Get database-specific terms for prompts
            db_terms = get_database_specific_terms(migration_name)

            # Create optimized validation prompt with only necessary information
            validation_prompt = create_identified_responsible_features_validation_prompt(
                identified_features=identified_features,
                original_source=original_source,
                ai_converted=ai_converted,
                actual_target=actual_target,
                deployment_error=deployment_error,
                db_terms=db_terms,
                decrypted_modules=decrypted_modules,
                validation_feedback=validation_feedback
                # Note: Removed keyword_mapping as it's not needed for validation
            )

            # Get AI validation using structured output
            llm_with_structure = self.llm.client.with_structured_output(IdentifiedFeaturesValidationOutput)
            validation_output = llm_with_structure.invoke(validation_prompt)

            # Convert to dict for easier handling
            validation_result = {
                "validation_results": [
                    {
                        "feature_name": result.feature_name,
                        "module_path": result.module_path,
                        "is_valid": result.is_valid,
                        "validation_reason": result.validation_reason
                    }
                    for result in validation_output.validation_results
                ],
                "overall_validation_passed": validation_output.overall_validation_passed,
                "validation_summary": validation_output.validation_summary,
                "feedback_for_reidentification": validation_output.feedback_for_reidentification
            }

            print(f"✅ AI validation completed - Overall passed: {validation_result['overall_validation_passed']}")
            if validation_result['overall_validation_passed']:
                print(f"✅ All {len(identified_features)} identified features are truly responsible")
            else:
                print(f"❌ Some identified features are not truly responsible - feedback provided for re-identification")

            return validation_result

        except Exception as e:
            print(f"❌ AI validation failed: {str(e)}")
            return {
                "validation_results": [],
                "overall_validation_passed": False,
                "validation_summary": f"AI validation failed: {str(e)}",
                "feedback_for_reidentification": f"Validation error occurred: {str(e)}. Please retry identification."
            }

    def log_features_validation_to_excel(self, state: Stage2WorkflowState, validation_result: Dict[str, Any]) -> None:
        """
        Log features validation results to Excel file.

        Args:
            state: Current workflow state
            validation_result: Validation results from AI
        """
        try:
            # Get statement number from current statement data
            combined_data = state.available_features_with_statements
            current_statement_index = getattr(state, 'current_statement_index', 0)
            current_statement_data = combined_data[current_statement_index]
            statement_number = current_statement_data.get('source_statement_number', 'Unknown')

            # Count validation results
            validation_results = validation_result.get('validation_results', [])
            valid_features = [r for r in validation_results if r.get('is_valid', False)]
            invalid_features = [r for r in validation_results if not r.get('is_valid', False)]

            # Prepare Excel data
            validation_data = {
                'Statement_Number': statement_number,
                'Total_Features_Identified': len(validation_results),
                'Validation_Status': 'SUCCESS' if validation_result.get('overall_validation_passed', False) else 'FAILED',
                'Valid_Features_Count': len(valid_features),
                'Invalid_Features_Count': len(invalid_features),
                'Valid_Features': str([f"{f['feature_name']}:{f['module_path']}" for f in valid_features]),
                'Invalid_Features': str([f"{f['feature_name']}:{f['module_path']}" for f in invalid_features]),
                'Validation_Summary': validation_result.get('validation_summary', ''),
                'Feedback_Generated': validation_result.get('feedback_for_reidentification', ''),
                'Timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Log to Excel
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                append_sheet_to_excel(
                    file_path=excel_path,
                    sheet_name="Features_Validation",
                    data=[validation_data]
                )
                print(f"📊 Validation results logged to Excel: {validation_data['Validation_Status']}")
            else:
                print("⚠️ No Excel path found - skipping validation logging")

        except Exception as e:
            print(f"❌ Failed to log validation to Excel: {str(e)}")

    # ==================== MODULE UPDATE NODES ====================

    def update_responsible_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 7: Update Responsible Modules Node.

        Purpose: Update Python modules based on identified responsible features using AI-driven analysis.

        Process:
            1. Get validated responsible features from previous node
            2. Read and decrypt original Python modules
            3. Use AI to analyze conversion gaps and enhance modules
            4. Store updated modules in Stage1_Metadata feature_modules folder
            5. Track all updates in Excel sheet for audit trail
            6. Preserve existing functionality while adding new capabilities

        Args:
            state: Stage2WorkflowState containing validated responsible features

        Returns:
            Dict containing updated modules and enhancement results
        """
        print("🔄 Starting Update Responsible Modules...")



        try:
            # Get validated responsible features from state
            responsible_features = getattr(state, 'responsible_features', [])
            current_statement_index = getattr(state, 'current_statement_index', 0)  # 0-based index



            # Get statement number from current statement data (same as apply_updated_modules)
            combined_data = state.available_features_with_statements
            current_statement_data = combined_data[current_statement_index]
            current_statement_number = current_statement_data.get('source_statement_number', current_statement_index + 1)

            attempt_number = getattr(state, 'current_attempt', 1)  # Keep attempt tracking for modules

            # Previous attempts are already saved when AI comparison fails
            # No need to save placeholder attempts here

            # Get AI comparison feedback for module refinement
            ai_comparison_feedback = getattr(state, 'ai_comparison_feedback', None)

            # Log feedback availability
            if ai_comparison_feedback:
                print(f"🔄 Using AI comparison feedback for module refinement...")
                print(f"📝 AI Comparison Feedback preview: {ai_comparison_feedback[:150]}...")
            else:
                print("📝 No feedback available - performing initial enhancement")

            if not responsible_features:
                print("⚠️ No validated responsible features found")
                return {
                    "updated_modules": [],
                    "current_statement": {}  # Empty dict for no features case
                }

            # Get current statement data from state
            combined_data = state.available_features_with_statements
            current_statement_data = combined_data[current_statement_index]
            print(f"📊 Processing statement {current_statement_number} with {len(responsible_features)} validated features")

            # Use the validated responsible features from state (already validated by previous node)
            print(f"🎯 Processing {len(responsible_features)} validated responsible features for statement {current_statement_number}")

            # Log the features being processed
            for idx, feature_data in enumerate(responsible_features, 1):
                # Handle both old format (feature_name, module_path) and new format (feature_name, module_path, keywords)
                if len(feature_data) == 3:
                    feature_name, module_path, feature_keywords = feature_data
                    print(f"   {idx}. {feature_name} -> {module_path} (Keywords: {feature_keywords})")
                else:
                    feature_name, module_path = feature_data
                    print(f"   {idx}. {feature_name} -> {module_path}")

            # Setup paths for Stage1_Metadata with statement-specific directory and attempt tracking
            paths_info = self.setup_module_update_paths(state, current_statement_number, attempt_number)

            # Use existing Stage 2 Excel file (following same pattern as other nodes)
            if not hasattr(state, 'stage2_excel_path') or not state.stage2_excel_path:
                print("⚠️ Stage 2 Excel file path not found in state - cannot add Module_Updates sheet")
                return {
                    "error": "Stage 2 Excel file path not found in state",
                    "current_statement": current_statement_data  # Pass current statement even in error case
                }

            updated_modules = []
            total_features = len(responsible_features)

            print(f"🔧 Starting update process for ALL {total_features} responsible features")
            print(f"📋 Features to update: {[f[0] for f in responsible_features]}")

            # Process each responsible feature (ALL n features must be updated)
            for idx, feature_data in enumerate(responsible_features, 1):
                # Handle both old format (feature_name, module_path) and new format (feature_name, module_path, keywords)
                if len(feature_data) == 3:
                    feature_name, module_path, feature_keywords = feature_data
                else:
                    feature_name, module_path = feature_data
                    feature_keywords = ''  # Default to empty if not available

                print(f"🔧 Updating module {idx}/{total_features}: {feature_name}")

                try:
                    # Read and decrypt original module
                    original_module_content = self.read_and_decrypt_module(
                        feature_name,
                        module_path,
                        state.migration_name,
                        state.cloud_category
                    )

                    if not original_module_content:
                        print(f"⚠️ Could not read module: {feature_name}")
                        continue

                    # Prepare AI analysis context with keywords from responsible features
                    ai_context = self.prepare_ai_analysis_context(
                        current_statement_data,
                        feature_name,
                        module_path,
                        original_module_content,
                        state.migration_name,
                        feature_keywords
                    )

                    # Get current attempt info
                    current_attempt = getattr(state, 'current_attempt', 1)

                    # Get in-memory attempt history for learning
                    attempt_history = self.get_attempt_history_for_learning(state)

                    # Use AI comparison feedback for module enhancement
                    if ai_comparison_feedback:
                        # AI comparison feedback available - use with attempt history
                        print("🔄 Enhancing module with AI comparison feedback and attempt history")
                        enhancement_result = self.ai_enhance_module(
                            ai_context,
                            ai_comparison_feedback,
                            attempt_history
                        )
                    else:
                        # Initial enhancement - no feedback
                        print("🔄 Performing initial module enhancement")
                        enhancement_result = self.ai_enhance_module(
                            ai_context,
                            None,
                            None
                        )

                    if enhancement_result and enhancement_result.get('enhanced_code'):
                        # Check if code actually changed
                        code_changed = enhancement_result.get('code_changed', False)
                        change_status = "✅ Enhanced" if code_changed else "⚠️ Unchanged"
                        print(f"🔧 Module '{feature_name}' status: {change_status}")

                        # Save updated module to feature_modules/ directory with attempt tracking
                        saved_module_path = self.save_updated_module(
                            feature_name,
                            module_path,
                            enhancement_result['enhanced_code'],
                            paths_info['feature_modules_dir'],
                            attempt_number  # Use attempt number for file naming
                        )

                        # Extract original feature name from module path (e.g., 'join' from 'Common/Statement/Pre/join.py')
                        original_feature_name = os.path.splitext(os.path.basename(module_path))[0]

                        # Create update result for state using original feature name for consistent lookup
                        update_result = {
                            "statement_number": current_statement_number,
                            "feature_name": original_feature_name,  # Use original feature name from module path
                            "module_path": module_path,
                            "keywords": feature_keywords,  # Add keywords for Excel logging
                            "updated_module_path": saved_module_path,
                            "original_code": original_module_content,
                            "updated_code": enhancement_result['enhanced_code'],
                            "enhancement_analysis": enhancement_result.get('analysis', {}),
                            "code_changed": enhancement_result.get('code_changed', False),
                            "attempt_number": attempt_number  # Use attempt number for tracking
                        }

                        updated_modules.append(update_result)

                        # Log to Excel using existing Stage 2 Excel file
                        self.log_module_update_to_excel(state.stage2_excel_path, update_result, "SUCCESS")

                        print(f"✅ Successfully updated module {idx}/{total_features}: {feature_name}")
                        print(f"💾 Saved to: {saved_module_path}")

                    else:
                        print(f"❌ Failed to enhance module {idx}/{total_features}: {feature_name}")
                        # Log failure to Excel
                        self.log_module_update_to_excel(
                            state.stage2_excel_path,
                            {
                                "statement_number": current_statement_number,
                                "feature_name": feature_name,
                                "module_path": module_path,
                                "keywords": feature_keywords,  # Add keywords for Excel logging
                                "attempt_number": attempt_number,
                                "error": "AI enhancement failed"
                            },
                            "FAILED"
                        )

                except Exception as e:
                    print(f"❌ Error updating module {idx}/{total_features} - {feature_name}: {str(e)}")
                    # Log error to Excel
                    self.log_module_update_to_excel(
                        state.stage2_excel_path,
                        {
                            "statement_number": current_statement_number,
                            "feature_name": feature_name,
                            "module_path": module_path,
                            "keywords": feature_keywords,  # Add keywords for Excel logging
                            "attempt_number": attempt_number,
                            "error": str(e)
                        },
                        "ERROR"
                    )

            # Summary of module changes
            changed_modules = [m for m in updated_modules if m.get('code_changed', False)]
            unchanged_modules = [m for m in updated_modules if not m.get('code_changed', False)]

            print(f"✅ Updated {len(updated_modules)} modules successfully for statement {current_statement_number}")
            print(f"   🔧 {len(changed_modules)} modules enhanced (logic changed)")
            print(f"   ⚠️ {len(unchanged_modules)} modules unchanged (AI reproduced same logic)")

            if changed_modules:
                print(f"   ✅ Enhanced modules: {', '.join([m['feature_name'] for m in changed_modules])}")
            if unchanged_modules:
                print(f"   ⚠️ Unchanged modules: {', '.join([m['feature_name'] for m in unchanged_modules])}")

            print(f"📁 All enhanced modules saved to: {paths_info['feature_modules_dir']}")
            print(f"📊 Excel tracking added to: {state.stage2_excel_path}")

            # Don't save attempt here - save it when AI comparison fails
            # This ensures attempt info is available for the next retry

            # Return updated modules in state for next node to use (avoiding re-reading/decrypting)
            return {
                "updated_modules": updated_modules,  # Store in state for next node
                "feature_modules_directory": paths_info['feature_modules_dir'],  # Directory path for file operations
                "current_statement": current_statement_data  # Pass current statement to next node for validation
            }

        except Exception as e:
            print(f"❌ Module update failed: {str(e)}")
            # Still need to increment attempt number even on error
            current_attempt = getattr(state, 'current_attempt', 1)
            ai_comparison_feedback = getattr(state, 'ai_comparison_feedback', None)
            attempt_number = current_attempt + 1 if ai_comparison_feedback else current_attempt

            return {
                "updated_modules": [],
                "error": str(e),
                "current_statement": {},  # Empty dict for error case
                "current_attempt": attempt_number  # Update attempt number in state
            }

    def update_responsible_modules_driver_approach(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 7: Update Responsible Modules using Driver Module Approach.

        This method implements the driver module approach where multiple responsible modules
        are combined into a single module for enhancement, then decomposed back with only
        functionally changed modules saved as attempt files.

        Args:
            state: Stage2WorkflowState containing validated responsible features

        Returns:
            Dict containing updated modules and enhancement results
        """
        print("🔄 Starting Update Responsible Modules (Driver Module Approach)...")

        try:
            # Get validated responsible features from state
            responsible_features = getattr(state, 'responsible_features', [])
            current_statement_index = getattr(state, 'current_statement_index', 0)

            # Get statement number from current statement data
            combined_data = state.available_features_with_statements
            current_statement_data = combined_data[current_statement_index]
            current_statement_number = current_statement_data.get('source_statement_number', current_statement_index + 1)

            attempt_number = getattr(state, 'current_attempt', 1)
            ai_comparison_feedback = getattr(state, 'ai_comparison_feedback', None)

            # Log feedback availability
            if ai_comparison_feedback:
                print(f"🔄 Using AI comparison feedback for module refinement...")
                print(f"📝 AI Comparison Feedback preview: {ai_comparison_feedback[:150]}...")
            else:
                print("📝 No feedback available - performing initial enhancement")

            if not responsible_features:
                print("⚠️ No validated responsible features found")
                return {
                    "updated_modules": [],
                    "current_statement": {}
                }

            print(f"📊 Processing statement {current_statement_number} with {len(responsible_features)} validated features")

            # Setup paths for Stage1_Metadata
            paths_info = self.setup_module_update_paths(state, current_statement_number, attempt_number)

            # Check Stage 2 Excel file path
            if not hasattr(state, 'stage2_excel_path') or not state.stage2_excel_path:
                print("⚠️ Stage 2 Excel file path not found in state")
                return {
                    "error": "Stage 2 Excel file path not found in state",
                    "current_statement": current_statement_data
                }

            # Read and decrypt all responsible modules and collect keywords
            responsible_modules_with_code = []
            all_keywords = []
            modules_info = {}  # Store module info for later use

            for feature_data in responsible_features:
                if len(feature_data) == 3:
                    feature_name, module_path, feature_keywords = feature_data
                else:
                    feature_name, module_path = feature_data
                    feature_keywords = ''

                print(f"📖 Reading module: {feature_name}")

                # Read and decrypt original module
                original_module_content = self.read_and_decrypt_module(
                    feature_name,
                    module_path,
                    state.migration_name,
                    state.cloud_category
                )

                if original_module_content:
                    responsible_modules_with_code.append((feature_name, module_path, original_module_content))

                    # Store module info for later use
                    modules_info[feature_name] = {
                        'module_path': module_path,
                        'keywords': feature_keywords,
                        'original_code': original_module_content
                    }

                    # Collect keywords
                    if feature_keywords:
                        all_keywords.append(feature_keywords)

                    print(f"✅ Successfully read module: {feature_name}")
                else:
                    print(f"⚠️ Could not read module: {feature_name}")

            if not responsible_modules_with_code:
                print("❌ No modules could be read successfully")
                return {
                    "updated_modules": [],
                    "current_statement": current_statement_data
                }

            # Initialize driver module enhancer
            driver_enhancer = DriverModuleEnhancer(self.llm_client)

            # Prepare AI analysis context with combined keywords
            combined_keywords = ", ".join(all_keywords) if all_keywords else "SQL transformation, database migration"
            combined_module_content = f"Combined module with {len(responsible_modules_with_code)} features: {', '.join([name for name, _, _ in responsible_modules_with_code])}"

            ai_context = self.prepare_ai_analysis_context(
                current_statement_data,
                "combined_driver_module",  # Use combined module name
                "driver_module_path",
                combined_module_content,  # Description of combined module
                state.migration_name,
                combined_keywords  # Combined keywords from all modules
            )

            # Add additional context for driver module
            ai_context['ai_comparison_feedback'] = ai_comparison_feedback
            ai_context['attempt_history'] = self.get_attempt_history_for_learning(state)

            # Execute driver module enhancement workflow
            enhancement_result = driver_enhancer.driver_module_enhancement_workflow(
                responsible_modules_with_code=responsible_modules_with_code,
                ai_context=ai_context,
                feature_modules_dir=paths_info['feature_modules_dir'],
                attempt_number=attempt_number
            )

            if enhancement_result['success']:
                # Process results for state and Excel logging
                updated_modules = []

                for enhanced_module in enhancement_result['enhanced_modules']:
                    feature_name = enhanced_module['feature_name']
                    module_info = modules_info.get(feature_name, {})

                    # Create update result for state
                    update_result = {
                        "statement_number": current_statement_number,
                        "feature_name": feature_name,
                        "module_path": enhanced_module['module_path'],
                        "keywords": module_info.get('keywords', ''),  # Use actual keywords
                        "updated_module_path": os.path.join(paths_info['feature_modules_dir'],
                                                           f"{feature_name}_attempt_{attempt_number}.py"),
                        "original_code": module_info.get('original_code', ''),  # Use actual original code
                        "updated_code": enhanced_module['enhanced_code'],
                        "enhancement_analysis": "Driver module approach - functional changes detected",
                        "code_changed": enhanced_module['code_changed'],
                        "attempt_number": attempt_number
                    }

                    updated_modules.append(update_result)

                    # Log to Excel
                    self.log_module_update_to_excel(state.stage2_excel_path, update_result, "SUCCESS")

                # Log unchanged modules to Excel as well
                for unchanged_module in enhancement_result['unchanged_modules']:
                    module_info = modules_info.get(unchanged_module, {})

                    unchanged_result = {
                        "statement_number": current_statement_number,
                        "feature_name": unchanged_module,
                        "module_path": module_info.get('module_path', ''),  # Use actual module path
                        "keywords": module_info.get('keywords', ''),  # Use actual keywords
                        "updated_module_path": "Using original module",
                        "original_code": module_info.get('original_code', ''),  # Use actual original code
                        "updated_code": "",  # No updated code for unchanged modules
                        "enhancement_analysis": "Driver module approach - no functional changes",
                        "code_changed": False,
                        "attempt_number": attempt_number
                    }

                    self.log_module_update_to_excel(state.stage2_excel_path, unchanged_result, "UNCHANGED")

                print(f"✅ Driver module enhancement completed successfully")
                print(f"   🔧 {enhancement_result['changed_modules_count']} modules enhanced (logic changed)")
                print(f"   ➖ {enhancement_result['unchanged_modules_count']} modules unchanged")
                print(f"   ✅ Enhanced modules: {', '.join(enhancement_result['saved_modules'])}")
                print(f"   ➖ Unchanged modules: {', '.join(enhancement_result['unchanged_modules'])}")
                print(f"📁 Enhanced modules saved to: {paths_info['feature_modules_dir']}")
                print(f"📊 Excel tracking added to: {state.stage2_excel_path}")

                return {
                    "updated_modules": updated_modules,
                    "feature_modules_directory": paths_info['feature_modules_dir'],
                    "current_statement": current_statement_data,
                    "driver_enhancement_result": enhancement_result
                }
            else:
                print(f"❌ Driver module enhancement failed: {enhancement_result.get('error', 'Unknown error')}")
                return {
                    "updated_modules": [],
                    "error": enhancement_result.get('error', 'Driver module enhancement failed'),
                    "current_statement": current_statement_data
                }

        except Exception as e:
            print(f"❌ Driver module enhancement failed: {str(e)}")
            return {
                "updated_modules": [],
                "error": str(e),
                "current_statement": {}
            }

    def setup_module_update_paths(self, state: Stage2WorkflowState, statement_number: int, attempt_number: int) -> Dict[str, str]:
        """
        Setup paths for Stage1_Metadata directory structure with statement-specific directories and attempt tracking.

        Args:
            state: Stage2WorkflowState containing migration details
            statement_number: Current statement number (1-based) for directory organization
            attempt_number: Current attempt number for tracking

        Returns:
            Dictionary containing path information
        """
        try:
            # Get cloud category from state
            cloud_category = getattr(state, 'cloud_category', 'cloud')

            # Get Stage1_Metadata path based on cloud category
            if cloud_category.lower() == 'local':
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get dynamic folder name based on process type
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            # Create directory structure: Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/{process_type}_feature_modules/{statement_number}
            feature_modules_base_dir = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            # Statement-specific directory
            feature_modules_dir = os.path.join(feature_modules_base_dir, str(statement_number))

            # Create directory if it doesn't exist
            os.makedirs(feature_modules_dir, exist_ok=True)

            print(f"📁 Feature modules directory ({feature_modules_folder}): {feature_modules_dir}")
            print(f"📊 Processing statement: {statement_number}, attempt: {attempt_number}")

            return {
                "feature_modules_dir": feature_modules_dir,
                "stage1_metadata_path": stage1_metadata_path
            }

        except Exception as e:
            print(f"❌ Error setting up module update paths: {str(e)}")
            raise

    def read_and_decrypt_module(self, feature_name: str, module_path: str, migration_name: str, cloud_category: str) -> str:
        """
        Read and decrypt Python module from QMigrator conversion modules.

        Args:
            feature_name: Name of the feature
            module_path: Path to the module
            migration_name: Migration name
            cloud_category: Cloud category for path selection

        Returns:
            Decrypted module content as string
        """
        try:
            # Get base path based on cloud category
            if cloud_category.lower() == 'local':
                base_path = Config.Qbook_Local_Path
            else:
                base_path = Config.Qbook_Path

            # Construct full module path
            # module_path already contains the full path including .py file
            full_module_path = os.path.join(
                base_path,
                'Conversion_Modules',
                migration_name,
                module_path
            )

            print(f"📖 Reading module from: {full_module_path}")

            if not os.path.exists(full_module_path):
                print(f"⚠️ Module file not found: {full_module_path}")
                return ""

            # Decrypt the module using the same method as other nodes
            # Using hardcoded decrypt key from the existing system
            decrypt_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='
            decrypted_content = decrypt_conversion_file(full_module_path, decrypt_key)

            print(f"✅ Successfully read and decrypted module: {feature_name}")
            return decrypted_content

        except Exception as e:
            print(f"❌ Error reading module {feature_name}: {str(e)}")
            return ""

    def combine_features_with_deduplication(self, available_features: List[tuple], responsible_features: List[tuple]) -> List[tuple]:
        """
        Combine available features and responsible features, removing duplicates.
        If same feature exists in both lists, responsible_features takes priority.

        Args:
            available_features: Features from QMigrator available features
            responsible_features: Features identified as responsible from CSV

        Returns:
            Combined list of unique features with responsible features taking priority
        """
        try:
            # Create a dictionary to handle deduplication
            # Key: feature_name, Value: (feature_name, module_path)
            combined_dict = {}

            # First add available features
            for feature_name, module_path in available_features:
                combined_dict[feature_name] = (feature_name, module_path)

            # Then add responsible features (will overwrite duplicates)
            for feature_data in responsible_features:
                # Handle both old format (feature_name, module_path) and new format (feature_name, module_path, keywords)
                if len(feature_data) == 3:
                    feature_name, module_path, feature_keywords = feature_data
                else:
                    feature_name, module_path = feature_data

                if feature_name in combined_dict:
                    print(f"🔄 Overriding {feature_name}: responsible feature takes priority")
                combined_dict[feature_name] = (feature_name, module_path)

            # Convert back to list
            combined_features = list(combined_dict.values())

            print(f"🔗 Feature combination summary:")
            print(f"   📋 Available features: {len(available_features)}")
            print(f"   🎯 Responsible features: {len(responsible_features)}")
            print(f"   🔗 Combined unique features: {len(combined_features)}")

            return combined_features

        except Exception as e:
            print(f"❌ Error combining features: {str(e)}")
            # Fallback: return available features only
            return available_features

    def read_and_decrypt_module_from_full_path(self, feature_name: str, full_module_path: str) -> str:
        """
        Read and decrypt a module file from a full path.

        Args:
            feature_name: Name of the feature module
            full_module_path: Full path to the module file

        Returns:
            Decrypted module code

        Raises:
            Exception: If module cannot be found or decrypted
        """
        if not os.path.exists(full_module_path):
            raise Exception(f"Module file not found: {full_module_path}")

        # Read encrypted module content
        with open(full_module_path, 'r', encoding='utf-8') as f:
            encrypted_content = f.read()

        if not encrypted_content.strip():
            raise Exception(f"Module file is empty: {full_module_path}")

        # Decrypt the module using existing decryption function
        decryption_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='
        decrypted_content = decrypt_conversion_file(full_module_path, decryption_key)

        if not decrypted_content.strip():
            raise Exception(f"Module decryption resulted in empty content: {feature_name}")

        return decrypted_content

    def prepare_ai_analysis_context(self, statement_data: Dict, feature_name: str, module_path: str, module_content: str, migration_name: str = '', feature_keywords: str = '') -> Dict:
        """
        Prepare context for AI analysis of module enhancement requirements.

        Args:
            statement_data: Current statement data with conversion details
            feature_name: Name of the feature being processed
            module_path: Path to the module
            module_content: Original module content
            migration_name: Migration name for database-specific terms
            feature_keywords: Keywords associated with this feature from keyword mapping

        Returns:
            Context dictionary for AI analysis
        """
        try:
            # Extract statement information
            original_source_statement = statement_data.get('original_source_statement', '')
            original_target_statement = statement_data.get('original_target_statement', '')
            ai_converted_statement = statement_data.get('ai_converted_statement', '')
            original_deployment_error = statement_data.get('original_deployment_error', '')

            # Create simple context - AI can analyze everything itself
            context = {
                'feature_name': feature_name,
                'module_path': module_path,
                'original_module_code': module_content,
                'keywords': feature_keywords,  # Keywords already provided from responsible features
                'conversion_context': {
                    'original_source_statement': original_source_statement,
                    'original_target_statement': original_target_statement,
                    'ai_converted_statement': ai_converted_statement,
                    'original_deployment_error': original_deployment_error,
                    'migration_name': migration_name  # Add migration name for database-specific terms
                }
            }

            return context

        except Exception as e:
            print(f"❌ Error preparing AI context: {str(e)}")
            return {}

    def get_attempt_history_for_learning(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Get in-memory attempt history for learning from previous attempts.

        Args:
            state: Current workflow state containing attempt history

        Returns:
            Dict containing all previous attempts for learning
        """
        try:
            # Get attempt history from state
            attempt_history = getattr(state, 'attempt_history', [])

            print(f"📊 Found {len(attempt_history)} attempts in memory")

            if not attempt_history:
                print("📊 No previous attempts found for learning")
                return {'all_attempts': [], 'total_attempts': 0}

            # Return all attempts for learning
            all_attempts = attempt_history

            print(f"📊 Retrieved {len(all_attempts)} attempts for learning")

            return {
                'all_attempts': all_attempts,
                'total_attempts': len(attempt_history)
            }

        except Exception as e:
            print(f"⚠️ Error getting attempt history: {str(e)}")
            return {'all_attempts': [], 'total_attempts': 0}

    # Removed create_failure_pattern_summary function - no longer needed since we keep all attempts detailed

    def save_attempt_to_memory(self, state: Stage2WorkflowState, attempt_number: int, updated_modules: List[Dict[str, Any]], ai_comparison_feedback: str = None, final_output: str = None):
        """
        Save the current attempt to in-memory storage for future learning.

        Args:
            state: Current workflow state
            attempt_number: Current attempt number
            updated_modules: List of modules used in this attempt
            ai_comparison_feedback: Feedback from AI comparison explaining why it failed
            final_output: The final output produced by this attempt
        """
        try:
            # Get current attempt history from state
            attempt_history = getattr(state, 'attempt_history', [])

            # Check if this attempt number already exists
            existing_attempt = any(
                attempt.get('attempt_number') == attempt_number
                for attempt in attempt_history
            )

            if existing_attempt:
                print(f"⚠️ Attempt {attempt_number} already exists in memory - skipping duplicate")
                return

            # Create attempt record
            attempt_record = {
                'attempt_number': attempt_number,
                'modules_used': [],
                'ai_feedback': ai_comparison_feedback or f"Attempt {attempt_number} failed - modules combination did not produce expected output",
                'final_output': final_output,
                'status': 'FAILED',
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Add module information
            for module in updated_modules:
                module_info = {
                    'module_name': module.get('feature_name', 'unknown'),
                    'module_code': module.get('updated_code', ''),
                    'file_path': module.get('updated_module_path', '')
                }
                attempt_record['modules_used'].append(module_info)

            # Append to attempt history
            attempt_history.append(attempt_record)

            # Update state
            state.attempt_history = attempt_history

            print(f"💾 Saved attempt {attempt_number} to memory ({len(updated_modules)} modules)")
            print(f"📊 Total attempts in memory: {len(attempt_history)}")

        except Exception as e:
            print(f"⚠️ Error saving attempt to memory: {str(e)}")

    def clear_attempt_history_memory(self, state: Stage2WorkflowState):
        """
        Clear in-memory attempt history when moving to next statement.

        Args:
            state: Current workflow state
        """
        try:
            # Clear attempt history from memory
            state.attempt_history = []
            print("🗑️ Cleared attempt history from memory for next statement")

        except Exception as e:
            print(f"⚠️ Error clearing attempt history from memory: {str(e)}")



    def ai_enhance_module(self, context: Dict, ai_comparison_feedback: Optional[str] = None, attempt_history: Optional[Dict[str, Any]] = None) -> Dict:
        """
        Use AI to enhance the Python module based on conversion requirements with complete attempt history learning.

        Args:
            context: Analysis context with module and conversion details
            ai_comparison_feedback: Optional feedback from AI statement comparison
            attempt_history: Optional complete previous attempts with module combinations and feedback

        Returns:
            Enhancement result with updated code and analysis
        """
        try:
            print(f"🤖 Starting AI enhancement for module: {context.get('feature_name', 'Unknown')}")

            # Get database-specific terms for prompts (clean names without versions)
            migration_name = context.get('conversion_context', {}).get('migration_name', '')
            db_terms = get_database_specific_terms(migration_name) if migration_name else {}

            # Extract conversion context data
            conversion_context = context.get('conversion_context', {})

            # Get feature context from context (migration_name already extracted above)
            feature_name = context.get('feature_name', '')
            keywords = context.get('keywords', '')

            # Create AI prompt using the pattern-based analysis approach with complete attempt history
            formatted_prompt = create_module_enhancement_prompt(
                original_module_code=context.get('original_module_code', ''),
                qmigrator_target_statement=conversion_context.get('original_target_statement', ''),
                ai_corrected_statement=conversion_context.get('ai_converted_statement', ''),
                deployment_error=conversion_context.get('original_deployment_error', ''),
                ai_comparison_feedback=ai_comparison_feedback,
                attempt_history=attempt_history or [],
                migration_name=migration_name,
                feature_name=feature_name,
                keywords=keywords,
                db_terms=db_terms
            )

            # Get AI response using structured output
            llm_with_structure = self.llm.client.with_structured_output(ModuleEnhancementOutput)

            print("🤖 Calling AI for module enhancement...")
            ai_response = llm_with_structure.invoke(formatted_prompt)

            if ai_response and ai_response.enhanced_code:
                print("✅ AI enhancement completed successfully")
                print(f"📝 Analysis: {ai_response.analysis[:100]}...")

                # Get AI's assessment of whether code was actually changed
                ai_claimed_changed = getattr(ai_response, 'code_changed', False)

                # Also verify by comparing actual code content (dual validation)
                original_module_code = context.get('original_module_code', '')
                original_code_normalized = re.sub(r'\s+', ' ', original_module_code.strip())
                enhanced_code_normalized = re.sub(r'\s+', ' ', ai_response.enhanced_code.strip())
                actually_changed = original_code_normalized != enhanced_code_normalized

                # Use both AI assessment and actual comparison for final decision
                code_changed = ai_claimed_changed and actually_changed

                if ai_claimed_changed and actually_changed:
                    print(f"✅ Module code was successfully enhanced (AI confirmed + code verified)")
                elif ai_claimed_changed and not actually_changed:
                    print(f"⚠️ AI claimed enhancement but code is identical (AI error)")
                elif not ai_claimed_changed and actually_changed:
                    print(f"⚠️ Code changed but AI didn't report it (AI error)")
                else:
                    print(f"⚠️ Module code was not changed (AI confirmed + code verified)")

                enhancement_result = {
                    'enhanced_code': ai_response.enhanced_code,
                    'analysis': ai_response.analysis,
                    'code_changed': code_changed
                }

                return enhancement_result

            else:
                print("❌ AI enhancement failed - no valid response")
                return {}

        except Exception as e:
            print(f"❌ AI enhancement error: {str(e)}")
            return {}

    def save_updated_module(self, feature_name: str, module_path: str, updated_code: str, feature_modules_dir: str, attempt_number: int) -> str:
        """
        Save the updated module to Stage1_Metadata feature_modules directory with attempt tracking.

        Args:
            feature_name: Name of the feature
            module_path: Original module path to extract filename
            updated_code: Updated module code
            feature_modules_dir: Directory to save the module
            attempt_number: Current attempt number

        Returns:
            Path to saved module file
        """
        try:
            # Extract original filename from module_path (e.g., 'raisenotice.py' from 'Common/Statement/Pre/raisenotice.py')
            original_filename = os.path.basename(module_path)
            # Remove .py extension and add attempt number
            base_name = os.path.splitext(original_filename)[0]  # 'raisenotice'
            module_filename = f"{base_name}_attempt_{attempt_number}.py"  # 'raisenotice_attempt_1.py'
            module_file_path = os.path.join(feature_modules_dir, module_filename)

            # Save the updated module
            with open(module_file_path, 'w', encoding='utf-8') as file:
                file.write(updated_code)

            print(f"💾 Saved updated module for feature '{feature_name}': {module_file_path}")
            return module_file_path

        except Exception as e:
            print(f"❌ Error saving updated module: {str(e)}")
            raise

    def get_original_module_path(self, feature_name: str, module_path: str, migration_name: str, cloud_category: str) -> str:
        """
        Get the full path to the original module.

        Args:
            feature_name: Name of the feature
            module_path: Module path
            migration_name: Migration name
            cloud_category: Cloud category

        Returns:
            Full path to original module
        """
        try:
            if cloud_category.lower() == 'local':
                base_path = Config.Qbook_Local_Path
            else:
                base_path = Config.Qbook_Path

            return os.path.join(
                base_path,
                'Conversion_Modules',
                migration_name,
                module_path,
                f"{feature_name}.py"
            )

        except Exception as e:
            print(f"❌ Error getting original module path: {str(e)}")
            return ""

    def log_module_update_to_excel(self, excel_file_path: str, update_data: Dict, status: str):
        """
        Log module update information to Excel file using append_sheet_to_excel function.

        Args:
            excel_file_path: Path to Excel file
            update_data: Update data to log
            status: Status of the update (SUCCESS, FAILED, ERROR)
        """
        try:
            # Extract essential data for Excel logging
            statement_number = update_data.get('statement_number', '')
            feature_name = update_data.get('feature_name', '')
            module_path = update_data.get('module_path', '')
            updated_module_path = update_data.get('updated_module_path', '')
            original_code = update_data.get('original_code', '')
            updated_code = update_data.get('updated_code', '')
            enhancement_analysis = str(update_data.get('enhancement_analysis', ''))
            attempt_number = update_data.get('attempt_number', 1)
            error_details = update_data.get('error', '')
            code_changed = update_data.get('code_changed', False)

            # Get keywords from update_data if available
            feature_keywords = update_data.get('keywords', 'N/A')

            # Simple enhancement status - just Enhanced or Not Enhanced
            enhancement_status = "Enhanced" if code_changed else "Not Enhanced"

            # Prepare simplified data for Excel sheet (only essential fields that actually exist)
            excel_data = [{
                "Statement_Number": statement_number,
                "Feature_Name": feature_name,
                "Module_Path": module_path,
                "Feature_Keywords": feature_keywords,
                "Updated_Module_Path": updated_module_path,
                "Enhancement_Status": enhancement_status,  # Enhanced or Not Enhanced
                "Original_Module_Code": original_code,
                "Updated_Python_Module": updated_code,
                "Analysis": enhancement_analysis,
                "Attempt_Number": attempt_number
            }]

            # Debug: Print what we're logging to Excel
            print(f"📊 Excel logging - Enhancement_Status: {excel_data[0].get('Enhancement_Status')}")
            print(f"📊 Excel logging - Code_Changed: {excel_data[0].get('Code_Changed')}")

            # Use existing append_sheet_to_excel function (same pattern as other nodes)
            append_sheet_to_excel(excel_file_path, "Module_Updates", excel_data)

        except Exception as e:
            print(f"❌ Error logging to Excel: {str(e)}")







    def compare_ai_statements(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Enhanced Step 11: Compare AI Statements Node.

        Purpose: Compare AI corrected output vs applied updated modules output using AI analysis
        to determine if they have similar target functionality.

        Enhanced Logic:
        1. Get AI corrected statement from Stage 1
        2. Get applied modules statement from current processing
        3. Use AI to compare functional equivalence
        4. If equivalent: proceed to next statement
        5. If not equivalent: generate feedback and retry module updates

        Returns:
            Dict containing enhanced comparison results and feedback
        """
        print("\n" + "="*80)
        print("🔍 ENHANCED AI STATEMENT COMPARISON")
        print("="*80)

        try:
            # Get statement data from state
            approved_statements = getattr(state, 'approved_statements', [])
            current_statement_index = getattr(state, 'current_statement_index', 0)
            updated_ai_converted_statement = getattr(state, 'updated_ai_converted_statement', '')

            if not approved_statements or current_statement_index >= len(approved_statements):
                print("⚠️ No current statement found for comparison")
                return {"ai_statements_match": False}

            current_statement = approved_statements[current_statement_index]
            oracle_statement = current_statement.get('original_source_statement', '')
            ai_corrected_statement = current_statement.get('ai_converted_statement', '')
            deployment_error = current_statement.get('original_deployment_error', '')

            print(f"📝 Comparing statements for statement {current_statement_index + 1}")
            print(f"🔍 Oracle Source: {oracle_statement[:100]}...")
            print(f"🎯 AI Corrected: {ai_corrected_statement[:100]}...")
            print(f"🔧 Applied Modules: {updated_ai_converted_statement[:100]}...")

            if not updated_ai_converted_statement:
                print("⚠️ No applied modules statement found")
                return {"ai_statements_match": False}

            if not ai_corrected_statement:
                print("⚠️ No AI corrected statement found")
                return {"ai_statements_match": False}

            # Prepare statement data for AI comparison - only the two outputs that need to be compared
            statement_data = {
                'ai_converted_statement': ai_corrected_statement,
                'updated_ai_converted_statement': updated_ai_converted_statement
            }

            # Get migration name for database-specific terms
            migration_name = getattr(state, 'migration_name', 'Oracle_Postgres14')

            # Use AI to perform comparison between AI corrected output and applied modules output
            comparison_result = self.ai_compare_statement_functionality(
                statement_data=statement_data,
                migration_name=migration_name
            )

            statements_match = comparison_result.get('statements_match', False)

            print(f"📊 Statements Match: {'YES' if statements_match else 'NO'}")

            # Set AI comparison feedback and increment attempt if statements don't match
            ai_comparison_feedback = None
            current_attempt = getattr(state, 'current_attempt', 1)

            if not statements_match:
                ai_comparison_feedback = comparison_result.get('explanation', 'Statements do not have similar target functionality')
                print(f"📝 AI Comparison Feedback: {ai_comparison_feedback[:100]}...")

                # Save the complete attempt info when AI comparison fails
                updated_modules = getattr(state, 'updated_modules', [])
                if updated_modules:
                    print(f"💾 Saving failed attempt {current_attempt} to memory with {len(updated_modules)} modules")
                    self.save_attempt_to_memory(
                        state,
                        current_attempt,
                        updated_modules,
                        ai_comparison_feedback,
                        updated_ai_converted_statement
                    )


                else:
                    print(f"⚠️ No updated modules found to save for attempt {current_attempt}")

                # Increment attempt number for retry
                current_attempt = current_attempt + 1
                print(f"🔄 Incrementing attempt number to {current_attempt} for retry")

            # Excel logging for AI statement comparison with both outputs
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                current_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                excel_data = [{
                    "Source_Statement_Number": current_statement.get('source_statement_number', current_statement_index + 1),
                    "Attempt_Number": getattr(state, 'current_attempt', 1),  # Log current attempt, not incremented
                    "Statements_Match": "YES" if statements_match else "NO",
                    "AI_Corrected_Output": ai_corrected_statement,
                    "Applied_Modules_Output": updated_ai_converted_statement,
                    "Analysis": comparison_result.get('explanation', 'No detailed analysis available'),
                    "Timestamp": current_timestamp
                }]

                append_sheet_to_excel(excel_path, "AI_Statement_Comparison", excel_data)
                print(f"📋 Logged AI statement comparison for statement {current_statement_index + 1}")

            return {
                "ai_statements_match": statements_match,
                "ai_comparison_feedback": ai_comparison_feedback,
                "current_attempt": current_attempt,  # Update attempt number in state
                "attempt_history": getattr(state, 'attempt_history', [])  # Preserve attempt history
            }

        except Exception as e:
            print(f"❌ Enhanced AI statement comparison failed: {str(e)}")
            return {
                "ai_statements_match": False,
                "error": str(e)
            }

    def ai_compare_statement_functionality(self, statement_data: Dict[str, Any],
                                         migration_name: str) -> Dict[str, Any]:
        """
        Use AI to compare functional equivalence between AI corrected and applied modules statements.
        Follows the same pattern as other AI analysis functions in the codebase.

        Args:
            statement_data: Dictionary containing all statement information
            migration_name: Migration name for database-specific terms

        Returns:
            Dict containing detailed comparison results
        """
        try:
            # Extract only the two statements that need to be compared
            ai_corrected_statement = statement_data.get('ai_converted_statement', '')
            applied_modules_statement = statement_data.get('updated_ai_converted_statement', '')

            # Get database-specific terms for prompts (clean names without versions)
            db_terms = get_database_specific_terms(migration_name)

            # Create AI analysis prompt using prompts folder - only comparing the two outputs
            analysis_prompt = create_ai_statement_comparison_prompt(
                ai_corrected_statement=ai_corrected_statement,
                applied_modules_statement=applied_modules_statement,
                db_terms=db_terms
            )

            # Use structured output for reliable AI analysis (same pattern as other nodes)
            structured_llm = self.llm.client.with_structured_output(StatementComparisonOutput)
            ai_result = structured_llm.invoke(analysis_prompt)

            print(f"🧠 AI comparison result: {'MATCH' if ai_result.statements_match else 'NO MATCH'}")
            if not ai_result.statements_match:
                print(f"📝 Feedback: {ai_result.explanation[:100]}...")

            return {
                "statements_match": ai_result.statements_match,
                "explanation": ai_result.explanation
            }

        except Exception as e:
            print(f"❌ AI comparison failed: {str(e)}")
            return {
                "statements_match": False,
                "explanation": f"AI comparison failed: {str(e)}. Manual review required."
            }



    def more_statements_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 12: More Statements Decision Node.

        Purpose: Determine if more statements need processing or if current statement needs retry.

        This function implements the statement-by-statement processing logic:
        - Process one statement through entire workflow
        - If statement succeeds: move to next statement
        - If statement fails and attempts < max: retry current statement
        - If statement fails and attempts = max: move to next statement
        - If no more statements: complete workflow

        Returns:
            Dict with workflow control flags
        """
        print("🔄 Starting More Statements Decision...")

        try:
            # Get current state information
            ai_statements_match = getattr(state, 'ai_statements_match', False)
            current_attempt = getattr(state, 'current_attempt', 1)
            max_attempts = getattr(state, 'max_attempts', 5)
            current_statement_index = getattr(state, 'current_statement_index', 0)

            # Get current statement data and total count
            combined_data = getattr(state, 'available_features_with_statements', [])
            total_statements = len(combined_data)
            current_statement_data = combined_data[current_statement_index] if combined_data and current_statement_index < len(combined_data) else {}

            print(f"📊 Current Status:")
            print(f"   📝 Statement: {current_statement_index + 1}/{total_statements}")
            print(f"   🔄 Attempt: {current_attempt}/{max_attempts}")
            print(f"   ✅ AI Statements Match: {ai_statements_match}")

            # Excel logging for statement decision tracking
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                current_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Decision logic based on workflow graph
            if ai_statements_match:
                # Current statement succeeded, check if more statements exist
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"✅ Statement {current_statement_index + 1} completed successfully")
                    print(f"📝 Moving to next statement {next_index + 1}/{total_statements}")

                    # Clear attempt history for completed statement
                    self.clear_attempt_history_memory(state)

                    # Log successful statement completion
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "MOVE_TO_NEXT",
                            "Status": "SUCCESS",
                            "Analysis": f"Statement completed successfully after {current_attempt} attempt(s)",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged successful completion of statement {current_statement_index + 1}")

                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "validation_feedback": None,  # Clear validation feedback for new statement
                        "ai_comparison_feedback": None,  # Clear AI comparison feedback for new statement
                        "current_statement": None,  # Clear current statement data
                        "responsible_features": None,  # Clear responsible features
                        "responsible_procedures": None,  # Clear responsible procedures
                        "updated_modules": None,  # Clear updated modules
                        "attempt_history": [],  # Clear attempt history for new statement

                        "updated_ai_converted_statement": None,  # Clear updated statement
                        "current_statement_id": None,  # Clear statement ID
                        "responsible_features_valid": False,  # Reset validation flags

                        "ai_statements_match": False,  # Reset comparison flag
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed successfully!")

                    # Log workflow completion
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "WORKFLOW_COMPLETE",
                            "Status": "SUCCESS",
                            "Analysis": f"All {total_statements} statements processed successfully",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged workflow completion")

                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }
            elif current_attempt < max_attempts:
                # Current statement failed, retry if attempts remaining
                next_attempt = current_attempt + 1
                print(f"❌ Statement {current_statement_index + 1} failed - retrying (attempt {next_attempt}/{max_attempts})")
                print(f"🔢 Attempt tracking: current={current_attempt}, next={next_attempt}, max={max_attempts}")

                # Log retry decision
                if excel_path:
                    excel_data = [{
                        "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                        "Attempt_Number": current_attempt,
                        "Decision": "RETRY_CURRENT",
                        "Status": "RETRY",
                        "Analysis": f"Statement failed, retrying attempt {next_attempt}/{max_attempts}",
                        "Timestamp": current_timestamp
                    }]
                    append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                    print(f"📋 Logged retry decision for statement {current_statement_index + 1}")

                return {
                    "current_attempt": next_attempt,
                    "workflow_action": "retry_current"
                }
            else:
                # Max attempts reached, move to next statement
                print(f"🛑 Statement {current_statement_index + 1} failed after {max_attempts} attempts")
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"📝 Moving to next statement {next_index + 1}/{total_statements}")

                    # Clear attempt history for failed statement (max attempts reached)
                    self.clear_attempt_history_memory(state)

                    # Log max attempts reached, moving to next
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "MOVE_TO_NEXT_MAX_ATTEMPTS",
                            "Status": "FAILED",
                            "Analysis": f"Statement failed after {max_attempts} attempts, moving to next statement",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged max attempts reached for statement {current_statement_index + 1}")

                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "validation_feedback": None,  # Clear validation feedback for new statement
                        "ai_comparison_feedback": None,  # Clear AI comparison feedback for new statement
                        "current_statement": None,  # Clear current statement data
                        "responsible_features": None,  # Clear responsible features
                        "responsible_procedures": None,  # Clear responsible procedures
                        "updated_modules": None,  # Clear updated modules
                        "attempt_history": [],  # Clear attempt history for new statement

                        "updated_ai_converted_statement": None,  # Clear updated statement
                        "current_statement_id": None,  # Clear statement ID
                        "responsible_features_valid": False,  # Reset validation flags

                        "ai_statements_match": False,  # Reset comparison flag
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed (some with max attempts)")

                    # Log final workflow completion with some failures
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "WORKFLOW_COMPLETE_WITH_FAILURES",
                            "Status": "COMPLETED",
                            "Analysis": f"All {total_statements} statements processed, some reached max attempts",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged final workflow completion")

                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }

        except Exception as e:
            print(f"❌ More Statements Decision failed: {str(e)}")
            return {
                "workflow_action": "complete",
                "error": str(e)
            }

    def apply_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 9: Apply Updated Modules Node.

        Purpose: Apply updated Python modules to transform statements using the enhanced modules.

        Process:
            1. Get after_type_casting_statement from QMigrator conversion results
            2. Load all available feature modules for the object
            3. Create mixed module set (updated + original modules)
            4. Apply statement-level features in correct order
            5. Apply post-procedure features
            6. Replace comment markers with original comments
            7. Generate updated_AI_Converted_Statement
            8. Store result in state for comparison

        Args:
            state: Stage2WorkflowState containing updated modules and current statement data

        Returns:
            Dict containing transformation results and updated statement
        """
        try:
            print("\n" + "="*80)
            print("🔧 STEP 9: APPLY UPDATED MODULES")
            print("="*80)

            # Get current statement data
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])

            if not available_features_with_statements or current_statement_index >= len(available_features_with_statements):
                print("❌ No current statement data available")
                return {
                    "modules_applied": False,
                    "updated_ai_converted_statement": "",
                    "error": "No current statement data available"
                }

            current_statement = available_features_with_statements[current_statement_index]
            current_statement_number = current_statement.get('source_statement_number', 1)
            current_attempt = getattr(state, 'current_attempt', 1)

            print(f"🎯 Processing statement {current_statement_number} (attempt {current_attempt})")

            # Step 1: Get after_type_casting_statement from QMigrator conversion results
            after_type_casting_statement = current_statement.get('statement_after_typecasting', '')
            if not after_type_casting_statement:
                print("❌ No after_type_casting_statement available")
                return {
                    "modules_applied": False,
                    "updated_ai_converted_statement": "",
                    "error": "No after_type_casting_statement available"
                }

            print(f"📝 Starting statement: {len(after_type_casting_statement)} characters")

            # Step 2: Get available features and responsible features
            available_features = current_statement.get('available_features', [])
            post_features = current_statement.get('post_features', [])
            responsible_features = getattr(state, 'responsible_features', [])

            print(f"📋 Available features (QMigrator): {len(available_features)}")
            print(f"📋 Responsible features (CSV identified): {len(responsible_features)}")
            print(f"📋 Post-procedure features: {len(post_features)}")

            # Step 2.1: Combine available features + responsible features (remove duplicates)
            combined_features = self.combine_features_with_deduplication(
                available_features, responsible_features
            )

            print(f"🔗 Combined features (total): {len(combined_features)}")
            if responsible_features:
                responsible_names = [f[0] for f in responsible_features]
                print(f"🎯 Responsible features to apply: {responsible_names}")

            # Step 3: Create mixed module set (updated + original modules)
            updated_modules = getattr(state, 'updated_modules', [])
            # Create mapping for updated modules using original feature names
            updated_modules_map = {module['feature_name']: module for module in updated_modules}

            print(f"🔧 Updated modules available: {list(updated_modules_map.keys())}")

            # Step 4: Apply statement-level features in correct order
            current_output = after_type_casting_statement
            applied_features = []

            print("🔄 Phase 1: Applying statement-level features (available + responsible)...")
            for feature_name, original_module_path in combined_features:
                # Get module code (updated or original)
                module_code = self.get_module_code_for_feature(
                    state, feature_name, current_statement_number, current_attempt,
                    original_module_path, updated_modules_map
                )

                # Show which module version is being used
                if feature_name in updated_modules_map:
                    updated_path = updated_modules_map[feature_name].get('updated_module_path', 'N/A')
                    print(f"🔧 Using updated module for {feature_name}: {updated_path}")
                else:
                    print(f"📁 Using original module for {feature_name}: {original_module_path}")

                # Apply the module
                previous_output = current_output
                print(f"🔍 MODULE INPUT for {feature_name}:")
                print(f"📝 Input: {previous_output}")
                current_output = self.apply_feature_module(current_output, feature_name, module_code, state)
                print(f"📤 MODULE OUTPUT for {feature_name}:")
                print(f"📝 Output: {current_output}")

                # Track if transformation actually occurred
                transformation_applied = current_output != previous_output
                applied_features.append({
                    'feature_name': feature_name,
                    'transformation_applied': transformation_applied,
                    'feature_type': 'statement',
                    'module_input': previous_output,
                    'module_output': current_output
                })

                # Log transformation
                if current_output != previous_output:
                    print(f"✅ {feature_name}: Statement transformed ({len(previous_output)} → {len(current_output)} chars)")
                else:
                    print(f"➖ {feature_name}: No transformation applied")

            # Step 5: Apply post-procedure features
            if post_features:
                print("🔄 Phase 2: Applying post-procedure features...")
                for feature_name, original_module_path in post_features:
                    # Get module code (updated or original)
                    module_code = self.get_module_code_for_feature(
                        state, feature_name, current_statement_number, current_attempt,
                        original_module_path, updated_modules_map
                    )

                    # Show which module version is being used
                    if feature_name in updated_modules_map:
                        updated_path = updated_modules_map[feature_name].get('updated_module_path', 'N/A')
                        print(f"🔧 Using updated module for {feature_name} (post): {updated_path}")
                    else:
                        print(f"📁 Using original module for {feature_name} (post): {original_module_path}")

                    # Apply the module
                    previous_output = current_output
                    print(f"🔍 MODULE INPUT for {feature_name} (post):")
                    print(f"📝 Input: {previous_output}")
                    current_output = self.apply_feature_module(current_output, feature_name, module_code, state)
                    print(f"📤 MODULE OUTPUT for {feature_name} (post):")
                    print(f"📝 Output: {current_output}")

                    # Track if transformation actually occurred
                    transformation_applied = current_output != previous_output
                    applied_features.append({
                        'feature_name': feature_name,
                        'transformation_applied': transformation_applied,
                        'feature_type': 'post',
                        'module_input': previous_output,
                        'module_output': current_output
                    })

                    # Log transformation
                    if current_output != previous_output:
                        print(f"✅ {feature_name} (post): Statement transformed ({len(previous_output)} → {len(current_output)} chars)")
                    else:
                        print(f"➖ {feature_name} (post): No transformation applied")

            # Step 6: Replace comment markers with original comments
            comments_dict = getattr(state, 'comments_dict', {})
            if comments_dict:
                print("🔄 Replacing comment markers with original comments...")
                current_output = replace_comment_markers(current_output, comments_dict)
                print(f"✅ Comment markers replaced successfully")
            else:
                print("ℹ️ No comments dictionary found, skipping comment replacement")

            # Step 7: Generate updated_AI_Converted_Statement
            updated_ai_converted_statement = current_output

            print(f"✅ Apply Updated Modules completed successfully")
            feature_names = [f['feature_name'] for f in applied_features]
            print(f"📊 Applied {len(applied_features)} features: {feature_names}")
            print(f"📝 Final statement: {len(updated_ai_converted_statement)} characters")

            # Step 8: Log to Excel and store result in state for comparison
            self.log_apply_modules_to_excel(state, combined_features, post_features, applied_features,
                                           updated_modules_map, current_statement_number, current_attempt)

            return {
                "modules_applied": True,
                "updated_ai_converted_statement": updated_ai_converted_statement,
                "applied_features": applied_features,
                "statement_level_features_applied": len([f for f in applied_features if f.get('feature_type') == 'statement']),
                "post_features_applied": len([f for f in applied_features if f.get('feature_type') == 'post']),
                "transformation_summary": {
                    "input_length": len(after_type_casting_statement),
                    "output_length": len(updated_ai_converted_statement),
                    "features_applied": len(applied_features)
                }
            }

        except Exception as e:
            error_msg = f"❌ Apply Updated Modules failed: {str(e)}"
            print(error_msg)
            return {
                "modules_applied": False,
                "updated_ai_converted_statement": "",
                "error": error_msg
            }

    def get_module_code_for_feature(self, state: Stage2WorkflowState, feature_name: str,
                                   current_statement_number: int, current_attempt: int,
                                   original_module_path: str, updated_modules_map: Dict[str, Any]) -> str:
        """
        Get module code - updated version from qm_feature_modules folder based on current attempt.

        Args:
            state: Stage2WorkflowState containing migration context
            feature_name: Name of the feature module
            current_statement_number: Current statement number being processed
            current_attempt: Current attempt number
            original_module_path: Path to original module
            updated_modules_map: Map of updated modules by feature name

        Returns:
            Decrypted module code ready for execution

        Raises:
            Exception: If module cannot be found or read
        """
        # Check if we have an updated module for this feature
        if feature_name in updated_modules_map:
            # Construct updated module path based on current attempt using cloud_category (same pattern as parent nodes)
            cloud_category = getattr(state, 'cloud_category', 'cloud')

            # Get Stage1_Metadata path based on cloud category
            if cloud_category.lower() == 'local':
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get dynamic folder name based on process type
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            qm_feature_modules_folder = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            updated_module_filename = f"{feature_name}_attempt_{current_attempt}.py"
            updated_module_path = os.path.join(
                qm_feature_modules_folder,
                str(current_statement_number),
                updated_module_filename
            )

            if os.path.exists(updated_module_path):
                print(f"🔧 Using updated module for {feature_name} (attempt {current_attempt}): {updated_module_path}")
                # Read updated module as plain text (not encrypted)
                try:
                    with open(updated_module_path, 'r', encoding='utf-8') as f:
                        updated_code = f.read()
                    if updated_code.strip():
                        return updated_code
                    else:
                        raise Exception(f"Updated module file is empty: {updated_module_path}")
                except Exception as e:
                    print(f"❌ Error reading updated module {feature_name}: {str(e)}")
                    raise Exception(f"Failed to read updated module {feature_name}: {str(e)}")
            else:
                print(f"📝 No updated module found for {feature_name} attempt {current_attempt}, checking if feature was updated in previous attempts")
                # Feature was identified as needing update but no file for current attempt exists
                # This means we should use original module
                pass

        # Use original module (original_module_path is relative path)
        print(f"📁 Using original module for {feature_name}: {original_module_path}")
        return self.read_and_decrypt_module(
            feature_name, original_module_path, state.migration_name, state.cloud_category
        )



    def apply_feature_module(self, statement: str, feature_name: str, module_code: str, state: Stage2WorkflowState) -> str:
        """
        Execute a feature module to transform the statement using importlib.util.
        """
        if not module_code.strip():
            raise Exception(f"Empty module code for {feature_name}")

        try:
            spec = importlib.util.spec_from_loader(feature_name, loader=None, origin="<in-memory>")
            module = importlib.util.module_from_spec(spec)

            # Execute the module code
            exec(module_code, module.__dict__)

        except Exception as exec_error:
            print(f"⚠️ Module execution error for {feature_name}: {str(exec_error)}")
            return statement

        # Try to call the function if it exists
        if hasattr(module, feature_name) and callable(getattr(module, feature_name)):
            try:
                schema_name = getattr(state, 'schema_name', None)
                if not schema_name:
                    print(f"⚠️ No schema_name found in state for {feature_name}")
                    return statement

                result = getattr(module, feature_name)(statement, schema_name)
                print(f"🔧 Module {feature_name} execution result: input_length={len(statement)}, output_length={len(str(result))}, same_content={result == statement}")
                return str(result)
            except Exception as func_error:
                print(f"⚠️ Function call error for {feature_name}: {str(func_error)}")
                return statement
        else:
            # Fallback: Try to get transformed statement from variables
            transformed_statement = (
                getattr(module, 'output_statement', None) or
                getattr(module, 'pre_output', None) or
                getattr(module, 'source_data', None) or
                statement
            )
            return str(transformed_statement)

    def log_apply_modules_to_excel(self, state: Stage2WorkflowState, combined_features: List[tuple],
                                  post_features: List[tuple], applied_features: List[Dict[str, Any]],
                                  updated_modules_map: Dict[str, Any], current_statement_number: int,
                                  current_attempt: int):
        """
        Log Apply Updated Modules results to Excel file.

        Creates a comprehensive sheet showing:
        - What features are available
        - What features were updated (with paths)
        - What features were applied
        - Module source information
        - Input and output for each module

        Args:
            state: Stage2WorkflowState containing Excel path
            combined_features: List of (feature_name, module_path) tuples for combined statement-level features (QMigrator + CSV responsible)
            post_features: List of (feature_name, module_path) tuples for post-procedure features
            applied_features: List of dicts with feature_name, transformation_applied, feature_type, module_input, module_output
            updated_modules_map: Map of updated modules by feature name
            current_statement_number: Current statement number being processed
            current_attempt: Current attempt number
        """
        try:
            if not hasattr(state, 'stage2_excel_path') or not state.stage2_excel_path:
                print("⚠️ No Excel path available for logging")
                return

            excel_data = []

            # Combine all features (combined statement-level + post-procedure)
            all_features = combined_features + post_features

            for feature_name, original_module_path in all_features:
                # Determine feature type
                feature_type = "Statement-Level" if (feature_name, original_module_path) in combined_features else "Post-Procedure"

                # Check if feature was updated
                is_updated = feature_name in updated_modules_map
                updated_module_path = ""
                updated_code_available = "No"

                if is_updated:
                    updated_module = updated_modules_map[feature_name]
                    updated_module_path = updated_module.get('updated_module_path', '')
                    updated_code = updated_module.get('updated_code', '')
                    updated_code_available = "Yes" if (updated_module_path or updated_code) else "No"

                # Check if feature was applied and if transformation occurred
                applied_info = next((f for f in applied_features if f['feature_name'] == feature_name), None)
                was_applied = applied_info is not None
                transformation_applied = applied_info.get('transformation_applied', False) if applied_info else False

                # Create application status
                if was_applied:
                    if transformation_applied:
                        application_status = "YES (applied)"
                    else:
                        application_status = "YES (but no change)"
                else:
                    application_status = "No"

                # Get input and output for this module from applied_info
                module_input = applied_info.get('module_input', 'N/A') if applied_info else 'N/A'
                module_output = applied_info.get('module_output', 'N/A') if applied_info else 'N/A'

                # Create record
                record = {
                    "Statement_Number": current_statement_number,
                    "Attempt_Number": current_attempt,
                    "Feature_Name": feature_name,
                    "Feature_Type": feature_type,
                    "Original_Module_Path": original_module_path,
                    "Is_Updated": "Yes" if is_updated else "No",
                    "Updated_Module_Path": updated_module_path,
                    "Updated_Code_Available": updated_code_available,
                    "Was_Applied": application_status,
                    "Module_Input": module_input,
                    "Module_Output": module_output,
                    "Transformation_Applied": "Yes" if transformation_applied else "No"
                }

                excel_data.append(record)

            # Use existing append_sheet_to_excel function (same pattern as other nodes)
            append_sheet_to_excel(state.stage2_excel_path, "Applied_Modules", excel_data)

            # Count transformations for summary
            transformations_applied = sum(1 for record in excel_data if record.get("Transformation_Applied") == "Yes")

            print(f"📋 Logged {len(excel_data)} feature application records to Applied_Modules sheet")
            print(f"📊 Module tracking: {transformations_applied}/{len(excel_data)} modules applied transformations")

        except Exception as e:
            print(f"❌ Error logging Apply Modules to Excel: {str(e)}")

    def complete_processing(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 13: Complete Processing Node.

        Purpose: Finalize Stage 2 workflow and prepare outputs.

        Returns:
            Dict containing final workflow results
        """
        print("🔄 Starting Complete Processing...")

        try:
          

            print("✅ Stage 2 workflow completed!")

            return {
                "workflow_completed": True,
                "final_excel_path": state.final_qbook_excel_path if state.final_qbook_excel_path else ""
            }

        except Exception as e:
            error_msg = f"❌ Complete Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "workflow_completed": False,
                "final_excel_path": ""
            }

    # ==================== UTILITY FUNCTIONS ====================





    def cleanup_process_feature_modules(self, state: Stage2WorkflowState) -> None:
        """
        Clean process-type-specific feature modules directory for fresh start when reprocessing.

        Deletes based on process type:
        - QMigrator: qbookv2/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/qm_feature_modules
        - QBook: qbookv2/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/qbook_feature_modules

        Args:
            state: Stage2WorkflowState containing object details and process type
        """
        try:
            # Determine paths based on cloud_category
            if state.cloud_category.lower() == "local":
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get process-type-specific folder name
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            # Build process-specific feature modules directory path
            feature_modules_dir = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            if os.path.exists(feature_modules_dir):
                print(f"🗑️ Cleaning {state.process_type} feature modules directory: {feature_modules_dir}")
                shutil.rmtree(feature_modules_dir)
                print(f"🗑️ Successfully deleted {feature_modules_folder} directory")
            else:
                print(f"📁 {feature_modules_folder} directory doesn't exist (first run): {feature_modules_dir}")

        except Exception as e:
            print(f"⚠️ Warning: Could not clean {state.process_type} feature modules directory: {str(e)}")


    def get_feature_modules_folder_name(self, process_type: str) -> str:
        """
        Get dynamic folder name based on process type.

        Args:
            process_type: "qmigrator" or "qbook"

        Returns:
            "qm_feature_modules" for qmigrator, "qbook_feature_modules" for qbook
        """
        if process_type == "qmigrator":
            return "qm_feature_modules"
        else:
            return "qbook_feature_modules"