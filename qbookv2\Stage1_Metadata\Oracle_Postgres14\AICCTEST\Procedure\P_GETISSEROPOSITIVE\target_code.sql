SET search_path TO AICCTEST;

CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE aicctest.P_GETISSEROPOSITIVE (IV_UHID IN varchar, IV_LOCATION IN varchar, OV_RESULT INOUT character)
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    V_BLOODNUMBER varchar(4000);
    V_LRN numeric;
    V_ISSEROTESTSID numeric;
    V_COUNT numeric;
    V_ISSEROTESTVALUES varchar(4000);
    I record;
BEGIN
    SET search_path TO AICCTEST;
    BEGIN
        SELECT
            Coalesce(BBAG.BLOODBAGNUMBER, DD.TOKENNO) INTO STRICT V_BLOODNUMBER
        FROM
            BB.DONORMASTER DM
        LEFT OUTER JOIN BB.DONORDETAILS DD ON DD.DONORID = DM.DONORID
    LEFT OUTER JOIN BB.BLOODBAG BBAG ON DD.VISITID = BBAG.VISITID
WHERE
    DM.UHID = IV_UHID
    AND DD.LOCATIONID = IV_LOCATION
LIMIT 1
--AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'
ORDER BY
    DM.CREATEDDATE DESC;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            V_BLOODNUMBER := NULL;
    END;
    IF V_BLOODNUMBER IS NULL THEN
        OV_RESULT := 'N';
    ELSE
        SELECT
            RR.LRN INTO STRICT V_LRN
        FROM
            LAB.RAISEREQUEST RR
        WHERE
            RR.PATIENTSERVICENO = V_BLOODNUMBER
            AND RR.PATIENTSERVICE = 'BLOODBANK'
            AND RR.LOCATIONID = IV_LOCATION
        ORDER BY
            RR.CREATEDDATE DESC
        LIMIT 1;
        FOR I IN (
            SELECT
                (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('E', XT.COLUMN_VALUE)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('E', XT.COLUMN_VALUE)))::text
                    END) AS SEROTESTIDS
            FROM
                TABLE (XMLSEQUENCE (extract(xml(REPLACE(('<X><E>' || (
                                        SELECT
                                            BCNF.CONFIGVALUE
                                        FROM BB.BBCONFIG BCNF
                                    WHERE
                                        UPPER(BCNF.CONFIG_KEY) = 'ISSEROPOSITIVETESTS') || '</E></X>'), ',', '</E><E>')), '/X/E'))) XT)
            LOOP
                EXIT
    WHEN I.SEROTESTIDS IS NULL;
        V_ISSEROTESTSID := TO_NUMBER(SUBSTR(I.SEROTESTIDS, 1, public.instr (I.SEROTESTIDS, '-') - 1));
        V_ISSEROTESTVALUES := SUBSTR(I.SEROTESTIDS, public.instr (I.SEROTESTIDS, '-') + 1);
        RAISE NOTICE '% ,%', V_ISSEROTESTSID, V_ISSEROTESTVALUES;
        SELECT
            COUNT(*) INTO STRICT V_COUNT
        FROM
            LAB.RAISEREQUEST RR
        LEFT OUTER JOIN LAB.REQUESTTESTS RT ON RR.LRN = RT.LRN
    LEFT OUTER JOIN LAB.LABREPORTS LR ON LR.REQUESTTESTID = RT.REQUESTTESTID
    LEFT OUTER JOIN LAB.TESTREPORTS TR ON TR.LABREPORTID = LR.LABREPORTID
WHERE
    RR.PATIENTSERVICE = 'BLOODBANK'
    AND RR.LRN = V_LRN
    AND RT.TESTID = V_ISSEROTESTSID
    AND UPPER(TR.RESULT) = V_ISSEROTESTVALUES
    AND RR.UHID = IV_UHID
    AND RR.LOCATIONID = IV_LOCATION;
    RAISE NOTICE '%', V_COUNT;
                END LOOP;
                IF V_COUNT > 0 THEN
                    OV_RESULT := 'Y';
                ELSE
                    OV_RESULT := 'N';
                    END IF;
        END IF;
END;
$BODY$;

