
  CREATE OR <PERSON><PERSON><PERSON>CE  PROCEDURE "AICCTEST"."P_UPDATELOCATIONID" 
(IN_FROMVAL NUMBER,
IN_TOVAL NUMBER)
AS

CURSOR C1
IS
SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM USER_TAB_COLS UTC
WHERE UTC.COLUMN_NAME LIKE 'LOCATIONID'
AND UTC.TABLE_NAME NOT IN (SELECT UM.MVIEW_NAME FROM USER_MVIEWS UM);

/*cursor c2
is
SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM USER_TAB_COLS UTC
WHERE UTC.COLUMN_NAME LIKE '%POSITION%'
AND UTC.TABLE_NAME NOT IN (SELECT UM.MVIEW_NAME FROM USER_MVIEWS UM
                           UNION
                           SELECT uv.view_name FROM user_views uv);*/

BEGIN

     FOR REC IN C1
     LOOP

         DBMS_OUTPUT.PUT_LINE('UPDATE '||REC.TABLE_NAME||
         ' SET '||REC.COLUMN_NAME||'='||IN_TOVAL||' WHERE '||
         REC.COLUMN_NAME||' = '||IN_FROMVAL||';');

     END LOOP;

  /*   DBMS_OUTPUT.PUT_LINE('-----------------------UPDATING POSITONID--------------------');

       FOR REC IN c2
     LOOP

         DBMS_OUTPUT.PUT_LINE('UPDATE '||REC.TABLE_NAME||
         ' SET '||REC.COLUMN_NAME||'='''||IV_toDEPT||'-'''||'||hr.f_desigfromposition('||rec.column_name||')'||
         ' WHERE '||
         REC.COLUMN_NAME||' = '''||IV_FROMDEPT||'-'''||'||hr.f_desigfromposition('||rec.column_name||');');

     END LOOP;
*/

END;
