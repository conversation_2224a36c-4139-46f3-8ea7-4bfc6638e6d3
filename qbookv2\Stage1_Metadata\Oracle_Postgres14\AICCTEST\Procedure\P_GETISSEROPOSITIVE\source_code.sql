
  CREATE OR <PERSON><PERSON><PERSON><PERSON>  PROCEDURE "AICCTEST"."P_GETI<PERSON>EROPOSITIVE" (IV_UHID     IN VARCHAR2,
                                                   IV_LOCATION IN VARCHAR2,
                                                   OV_RESULT   OUT CHARACTER) AS
  V_BLOODNUMBER      VARCHAR2(4000);
  V_LRN              NUMBER;
  V_ISSEROTESTSID    NUMBER;
  V_COUNT            NUMBER;
  V_ISSEROTESTVALUES VARCHAR2(4000);
BEGIN

  BEGIN
    SELECT NVL(BBAG.BLOODBAGNUMBER, DD.TOKENNO)
      INTO V_BLOODNUMBER
      FROM BB.DONORMASTER DM
      LEFT OUTER JOIN BB.DONORDETAILS DD
        ON DD.DONORID = DM.DONORID
      LEFT OUTER JOIN BB.BLOODBAG BBAG
        ON DD.VISITID = BBAG.VISITID
     WHERE DM.UHID = IV_UHID
       AND DD.LOCATIONID = IV_LOCATION
       AND ROWNUM < 2
    --AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'
     ORDER BY DM.CREATEDDATE DESC;

  EXCEPTION
    WHEN NO_DATA_FOUND THEN
      V_BLOODNUMBER := NULL;

  END;

  IF V_BLOODNUMBER IS NULL THEN

    OV_RESULT := 'N';

  ELSE

    SELECT RR.LRN
      INTO V_LRN
      FROM LAB.RAISEREQUEST RR
     WHERE RR.PATIENTSERVICENO = V_BLOODNUMBER
       AND RR.PATIENTSERVICE = 'BLOODBANK'
       AND RR.LOCATIONID = IV_LOCATION
       AND ROWNUM < 2
     ORDER BY RR.CREATEDDATE DESC;

    FOR I IN (SELECT EXTRACTVALUE(XT.COLUMN_VALUE, 'E') AS SEROTESTIDS
                FROM TABLE(XMLSEQUENCE(EXTRACT(XMLTYPE(REPLACE(('<X><E>' ||
                                                               (SELECT BCNF.CONFIGVALUE
                                                                   FROM BB.BBCONFIG BCNF
                                                                  WHERE UPPER(BCNF.CONFIG_KEY) =
                                                                        'ISSEROPOSITIVETESTS') ||
                                                               '</E></X>'),
                                                               ',',
                                                               '</E><E>')),
                                               '/X/E'))) XT) LOOP

      EXIT WHEN I.SEROTESTIDS IS NULL;

      V_ISSEROTESTSID    := TO_NUMBER(SUBSTR(I.SEROTESTIDS,
                                             1,
                                             INSTR(I.SEROTESTIDS, '-') - 1));
      V_ISSEROTESTVALUES := SUBSTR(I.SEROTESTIDS,
                                   INSTR(I.SEROTESTIDS, '-') + 1);

      DBMS_OUTPUT.PUT_LINE(V_ISSEROTESTSID || ',' || V_ISSEROTESTVALUES);

      SELECT COUNT(*)
        INTO V_COUNT
        FROM LAB.RAISEREQUEST RR
        LEFT OUTER JOIN LAB.REQUESTTESTS RT
          ON RR.LRN = RT.LRN
        LEFT OUTER JOIN LAB.LABREPORTS LR
          ON LR.REQUESTTESTID = RT.REQUESTTESTID
        LEFT OUTER JOIN LAB.TESTREPORTS TR
          ON TR.LABREPORTID = LR.LABREPORTID
       WHERE RR.PATIENTSERVICE = 'BLOODBANK'
         AND RR.LRN = V_LRN
         AND RT.TESTID = V_ISSEROTESTSID
         AND UPPER(TR.RESULT) = V_ISSEROTESTVALUES
         AND RR.UHID = IV_UHID
         AND RR.LOCATIONID = IV_LOCATION;

      DBMS_OUTPUT.PUT_LINE(V_COUNT);

    END LOOP;

    IF V_COUNT > 0 THEN

      OV_RESULT := 'Y';

    ELSE
      OV_RESULT := 'N';

    END IF;

  END IF;

END;


