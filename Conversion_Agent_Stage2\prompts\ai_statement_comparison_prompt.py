"""
AI Statement Comparison Prompt for Stage 2 Processing.

This module creates prompts for AI-driven comparison of AI corrected output vs applied modules output
to determine if they have similar target functionality.
"""

def create_ai_statement_comparison_prompt(
    ai_corrected_statement: str,
    applied_modules_statement: str,
    db_terms: dict
) -> str:
    """
    Create a simplified prompt for AI-driven statement comparison.

    Args:
        ai_corrected_statement: AI corrected PostgreSQL statement from Stage 1
        applied_modules_statement: Statement after applying updated modules
        db_terms: Database-specific terminology

    Returns:
        str: Simplified comparison prompt for AI analysis
    """

    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']

    prompt = f"""
You are a SENIOR {expert_title} with expertise in {target_db} statement comparison.

Your task is to compare two {target_db} statements to determine if they are essentially the same with only minor formatting differences.

CORE COMPARISON TASK:
Since both statements are already in {target_db} format, focus on:
1. **STATEMENT SIMILARITY**: Determine if both statements are essentially the same {target_db} code
2. **SYNTAX MATCHING**: Check if the core SQL logic and structure are the same
3. **FORMATTING TOLERANCE**: Allow for minor differences in whitespace, case, and formatting
4. **LOGIC EQUIVALENCE**: Verify both statements implement the same database operations

COMPARISON CONTEXT:
==================
**Target Database**: {target_db}

**AI Corrected {target_db} Statement** (Expected):
{ai_corrected_statement}

**Applied Modules {target_db} Statement** (Actual):
{applied_modules_statement}

COMPARISON CRITERIA:
===================

1. **CORE LOGIC MATCHING**:
   - Do both statements implement the same SQL operations and logic?
   - Are the main database functions and operations equivalent?
   - Do both statements process the same data in the same way?

2. **SQL CLAUSE ORDER VALIDATION**:
   - **CRITICAL**: SQL clause order must be identical for functional equivalence
   - All SQL clauses must appear in the same relative positions between statements
   - Clause positioning affects SQL execution behavior and results

3. **DIFFERENCES TO IGNORE**:
   - **ALWAYS IGNORE**: All whitespace differences (spaces, tabs, line breaks)
   - **ALWAYS IGNORE**: All case differences (uppercase vs lowercase)
   - **ALWAYS IGNORE**: Formatting and indentation differences
   - Minor syntax variations that don't change functionality
   - Variable name differences if the logic is the same

4. **UNACCEPTABLE DIFFERENCES**:
   - Different SQL functions or operations
   - Different data processing logic
   - Missing or extra functionality
   - Different result sets or outputs
   - **CRITICAL**: Different ordering of SQL clauses that affects execution behavior

COMPARISON METHODOLOGY:
======================

**STEP 1: NORMALIZE FOR FORMATTING ONLY**
- Remove extra whitespace, tabs, and normalize case for comparison
- **PRESERVE the relative positioning and ordering of all elements**
- Focus on SQL tokens and their sequential order
- **CRITICAL**: Maintain the structural sequence while ignoring formatting differences

**STEP 2: STRUCTURAL SEQUENCE ANALYSIS**
- **CRITICAL**: Compare the exact sequential order of elements in both statements
- Verify that elements appear in the same relative positions
- Check if the structural flow and element arrangement are identical
- **IMPORTANT**: Different element ordering = Different statements
- Ensure the sequence of operations is identical between both statements

**STEP 3: EQUIVALENCE DECISION**
- If core logic is the same AND element sequence is identical: statements match
- If core logic differs OR element sequence differs: statements don't match
- **CRITICAL**: Different element ordering = NO MATCH (even if same elements exist)
- **CRITICAL**: Different positioning of any elements = NO MATCH
- Focus on both functional equivalence AND structural sequence equivalence

COMPARISON GUIDELINES:
=====================

**EQUIVALENT STATEMENTS** must have:
- Same core SQL operations and logic
- Same data processing approach
- **CRITICAL**: Identical element sequence and positioning
- **CRITICAL**: Same relative order of all components
- **NOTE**: Only whitespace, formatting, and case differences are ignored

**NON-EQUIVALENT STATEMENTS** have:
- Different SQL operations or functions
- Different data processing logic
- **CRITICAL**: Different element ordering or positioning
- **CRITICAL**: Different sequence of components (even if same components exist)
- Missing or extra functionality

DECISION CRITERIA:
=================

**STATEMENTS MATCH** (return true):
✅ Both statements implement the same SQL logic and operations
✅ Core functionality is equivalent (minor syntax differences OK)
✅ Same data processing approach and result structure
✅ **CRITICAL**: Elements appear in identical sequential order
✅ **CRITICAL**: Same relative positioning of all components
✅ **IGNORED**: Only formatting, whitespace, tabs, line breaks, and case differences
✅ Variable naming differences are acceptable if logic is the same

SEQUENCE SENSITIVITY:
====================
- If Statement A has [ELEMENT_X] then [ELEMENT_Y]
- And Statement B has [ELEMENT_Y] then [ELEMENT_X]
- These are NOT equivalent (different sequence = different statements)

**STATEMENTS DON'T MATCH** (return false):
❌ Fundamentally different SQL operations or logic
❌ Missing or extra functionality between the statements
❌ Different data processing approaches that would yield different results
❌ Incompatible SQL syntax or structure
❌ **CRITICAL**: Different SQL clause ordering that affects execution behavior

OUTPUT FORMAT (JSON):
====================
{{
  "statements_match": <boolean - true if both statements are essentially the same with only minor formatting differences, false if they have significant functional differences>,
  "explanation": "<brief analysis of the comparison. If statements match, explain why they are equivalent. If they don't match, explain the key functional differences that need to be addressed>"
}}

COMPARISON FOCUS:
================
- **Normalization First**: Always normalize both statements by removing whitespace/case differences
- **Core Logic**: Compare normalized statements for same SQL operations
- **Clause Order**: Verify clause ordering in normalized versions
- **Functional Equivalence**: Focus on whether normalized statements would produce the same results

Remember:
1. **ALWAYS NORMALIZE**: Remove all whitespace, tabs, line breaks, and convert to same case
2. **COMPARE NORMALIZED**: Only compare the normalized versions of both statements
3. **IGNORE FORMATTING**: Completely ignore all formatting and case differences
4. Focus on core SQL logic equivalence of normalized statements, not exact text matching.
"""

    return prompt
