import re

def join(data, sch):
    original_condition = []
    if re.search(r'\(\s*\+\s*\)', data, flags=re.DOTALL | re.I):
        join_statements_collection = []
        join_tables_replacement = []

        from_semi = re.findall(r'\bfrom\b.*?;', data, flags=re.DOTALL | re.I)
        for from_to_semicolon in from_semi:
            from_where = re.findall(r'\bfrom\b.*?\bwhere\b', from_to_semicolon, flags=re.DOTALL | re.I)
            where_clause = re.findall(r'\bwhere\b(.*?);', from_to_semicolon, flags=re.DOTALL | re.I)
            for where in where_clause:
                split_pre_def = re.split(r'\bor\b|\band\b', where, flags=re.DOTALL | re.I)
                for split in split_pre_def:
                    if re.search(r'\border\b', split, flags=re.DOTALL | re.I):
                        split = re.split(r'\border\b', split, flags=re.DOTALL | re.I)[0]
                    if re.search(r'\(\s*\+\s*\)', split, flags=re.DOTALL | re.I):
                        if '=' in split:
                            split_values = split.split('=')
                            if '+' in split_values[1]:
                                if '.' in split_values[1]:
                                    split_values_alias = split_values[1].split('.')[0]
                                    for fw in from_where:
                                        if re.search(rf'\b{split_values_alias.strip()}\b', fw, flags=re.DOTALL | re.I):
                                            table = re.findall(rf'\S+\s*{split_values_alias}\b', fw, flags=re.DOTALL | re.I)
                                            if table:
                                                if ',' in table[0]:
                                                    table = [i.strip().split(',')[1].strip() for i in table if ',' in i]
                                                split_modification = re.sub(r'\(\s*\+\s*\)', '', split, flags=re.DOTALL | re.I)
                                                where_replace = re.sub(rf'(?:\band\b|\bor\b)\s*{re.escape(split.strip())}', '', where, flags=re.DOTALL | re.I)
                                                where_replace = re.sub(rf'{re.escape(split.strip())}', '', where_replace, flags=re.DOTALL | re.I)
                                                original_condition.append(split.strip())
                                                data = data.replace(where, where_replace)
                                                statement_formation = "LEFT JOIN " + list(set(table))[0] + " ON " + split_modification
                                                join_statements_collection.append(statement_formation)
                                                join_tables_replacement.append(table[0])
                            if '+' in split_values[0]:
                                if '.' in split_values[0]:
                                    split_values_alias = split_values[0].split('.')[0]
                                    for fw in from_where:
                                        if re.search(rf'\b{split_values_alias.strip()}\b', fw, flags=re.DOTALL | re.I):
                                            table = re.findall(rf'\S+\s*{split_values_alias}\b', fw, flags=re.DOTALL | re.I)
                                            if table:
                                                if ',' in table[0]:
                                                    table = [i.strip().split(',')[1].strip() for i in table if ',' in i]
                                                split_modification = re.sub(r'\(\s*\+\s*\)', '', split, flags=re.DOTALL | re.I)
                                                where_replace = re.sub(rf'(?:\band\b|\bor\b)\s*{re.escape(split.strip())}', '', where, flags=re.DOTALL | re.I)
                                                where_replace = re.sub(rf'{re.escape(split.strip())}', '', where_replace, flags=re.DOTALL | re.I)

                                                data = data.replace(where, where_replace)
                                                original_condition.append(split.strip())
                                                statement_formation = "RIGHT JOIN " + list(set(table))[0] + " ON " + split_modification
                                                join_statements_collection.append(statement_formation)
                                                join_tables_replacement.append(table[0])

            for from_where_replacements in from_where:
                if join_statements_collection:
                    from_where_modification = re.sub(r'\bwhere\b', ''.join(list(set(join_statements_collection))).strip() + '\nwhere ', from_where_replacements, flags=re.DOTALL | re.I)
                    for tables in join_tables_replacement:
                        from_where_modification = re.sub(rf',\s*\b{tables}\b', '', from_where_modification, flags=re.DOTALL | re.I)
                    data = data.replace(from_where_replacements, from_where_modification)
    if original_condition:
        for org in list(set(original_condition)):
            if re.search(rf'\b{re.escape(org.strip())}', data, flags=re.DOTALL | re.I):
                data = re.sub(rf'{re.escape(org.strip())}', '', data, flags=re.DOTALL)
        data = re.sub(r'(?:\bwhere\b|\bor\b|\band\b)\s*;', ';', data, flags=re.DOTALL | re.I)
        data = re.sub(r'(?:\bwhere\s*\bor\b|\bwhere\s*and\b)', ' WHERE ', data, flags=re.DOTALL | re.I)
    and_mofication = re.findall(r'\band\s*and\b.*?\border\b', data, flags=re.DOTALL | re.I)
    for and_iter in and_mofication:
        if not re.search('=', and_iter, flags=re.DOTALL):
            and_iter1 = re.sub(rf'{and_iter}', ' ORDER ', and_iter, flags=re.DOTALL | re.I)
            data = data.replace(and_iter, and_iter1)

    # Enhancement: Ensure LIMIT clause is after ORDER BY
    data = re.sub(r'(ORDER BY .*?)(LIMIT \d+)', r'\1 \2', data, flags=re.DOTALL | re.I)

    return data