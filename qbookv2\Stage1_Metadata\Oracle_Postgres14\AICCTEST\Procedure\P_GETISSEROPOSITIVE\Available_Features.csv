Statement_Number,Statement_After_Typecasting,Statement_Level_Output,Available_Features,Post_Features
1,"CREATE OR REPLACE PROCEDURE ""AICCTEST"".""P_GETISSEROPOSITIVE""(IV_UHID IN varchar,IV_LOCATION IN varchar,OV_RESULT inout CHARACTER)
as","CREATE OR REPLACE PROCEDURE ""AICCTEST"".""P_GETISSEROPOSITIVE""(IV_UHID IN varchar,IV_LOCATION IN varchar,OV_RESULT inout CHARACTER)
as",[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
2,"as
V_BLOODNUMBER varchar(4000);","as
V_BLOODNUMBER varchar(4000);",[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
3,V_LRN numeric;,V_LRN numeric;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
4,V_ISSEROTESTSID numeric;,V_ISSEROTESTSID numeric;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
5,V_COUNT numeric;,V_COUNT numeric;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
6,V_ISSEROTESTVALUES varchar(4000);,V_ISSEROTESTVALUES varchar(4000);,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
7,"BEGIN
BEGIN
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
limit 1
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC;","BEGIN
BEGIN
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
limit 1
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC;","[('nvl', 'Common/Statement/Pre/nvl.py'), ('join', 'Common/Statement/Pre/join.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
8,"EXCEPTION
WHEN NO_DATA_FOUND THEN
V_BLOODNUMBER :=null;","EXCEPTION
WHEN NO_DATA_FOUND THEN
V_BLOODNUMBER :=null;",[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
9,END;,END;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
10,"IF V_BLOODNUMBER
is NULL THEN
OV_RESULT := 'N';","IF V_BLOODNUMBER
is NULL THEN
OV_RESULT := 'N';",[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
11,"ELSE
SELECT RR.LRN
INTO V_LRN
FROM LAB.RAISEREQUEST RR
WHERE RR.PATIENTSERVICENO = V_BLOODNUMBER
AND RR.PATIENTSERVICE = 'BLOODBANK'
AND RR.LOCATIONID = IV_LOCATION
limit 1
ORDER BY RR.CREATEDDATE DESC;","ELSE
SELECT RR.LRN
INTO V_LRN
FROM LAB.RAISEREQUEST RR
WHERE RR.PATIENTSERVICENO = V_BLOODNUMBER
AND RR.PATIENTSERVICE = 'BLOODBANK'
AND RR.LOCATIONID = IV_LOCATION
limit 1
ORDER BY RR.CREATEDDATE DESC;","[('join', 'Common/Statement/Pre/join.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
12,"FOR I IN(SELECT
(case when(select unnest(xpath('E',XT.COLUMN_VALUE)))::text='' then null
else(select unnest(xpath('E',XT.COLUMN_VALUE)))::text end)
as SEROTESTIDS
FROM TABLE(XMLSEQUENCE(extract(xml(REPLACE(('<X><E>' ||
(SELECT BCNF.CONFIGVALUE
FROM BB.BBCONFIG BCNF
WHERE UPPER(BCNF.CONFIG_KEY)=
'ISSEROPOSITIVETESTS')||
'</E></X>'),',','</E><E>')),'/X/E')))XT)LOOP
EXIT WHEN I.SEROTESTIDS
is NULL;","FOR I IN(SELECT
(case when(select unnest(xpath('E',XT.COLUMN_VALUE)))::text='' then null
else(select unnest(xpath('E',XT.COLUMN_VALUE)))::text end)
as SEROTESTIDS
FROM TABLE(XMLSEQUENCE(extract(xml(REPLACE(('<X><E>' ||
(SELECT BCNF.CONFIGVALUE
FROM BB.BBCONFIG BCNF
WHERE UPPER(BCNF.CONFIG_KEY)=
'ISSEROPOSITIVETESTS')||
'</E></X>'),',','</E><E>')),'/X/E')))XT)LOOP
EXIT WHEN I.SEROTESTIDS
is NULL;","[('join', 'Common/Statement/Pre/join.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
13,"V_ISSEROTESTSID := TO_NUMBER(SUBSTR(I.SEROTESTIDS,1,INSTR(I.SEROTESTIDS,'-')- 1));","V_ISSEROTESTSID := TO_NUMBER(SUBSTR(I.SEROTESTIDS,1,public.instr(I.SEROTESTIDS,'-')- 1));","[('instr', 'Common/Statement/Pre/instr.py'), ('to_number', 'Common/Statement/Pre/to_number.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
14,"V_ISSEROTESTVALUES := SUBSTR(I.SEROTESTIDS,INSTR(I.SEROTESTIDS,'-')+ 1);","V_ISSEROTESTVALUES := SUBSTR(I.SEROTESTIDS,public.instr(I.SEROTESTIDS,'-')+ 1);","[('instr', 'Common/Statement/Pre/instr.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
15,"Raise Notice '% ,%',V_ISSEROTESTSID,V_ISSEROTESTVALUES;","Raise Notice '% ,%',V_ISSEROTESTSID,V_ISSEROTESTVALUES;","[('raisenotice', 'Common/Statement/Pre/raisenotice.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
16,"SELECT COUNT(*)
INTO V_COUNT
FROM LAB.RAISEREQUEST RR
LEFT OUTER JOIN LAB.REQUESTTESTS RT
ON RR.LRN = RT.LRN
LEFT OUTER JOIN LAB.LABREPORTS LR
ON LR.REQUESTTESTID = RT.REQUESTTESTID
LEFT OUTER JOIN LAB.TESTREPORTS TR
ON TR.LABREPORTID = LR.LABREPORTID
WHERE RR.PATIENTSERVICE = 'BLOODBANK'
AND RR.LRN = V_LRN
AND RT.TESTID = V_ISSEROTESTSID
AND UPPER(TR.RESULT)= V_ISSEROTESTVALUES
AND RR.UHID = IV_UHID
AND RR.LOCATIONID = IV_LOCATION;","SELECT COUNT(*)
INTO V_COUNT
FROM LAB.RAISEREQUEST RR
LEFT OUTER JOIN LAB.REQUESTTESTS RT
ON RR.LRN = RT.LRN
LEFT OUTER JOIN LAB.LABREPORTS LR
ON LR.REQUESTTESTID = RT.REQUESTTESTID
LEFT OUTER JOIN LAB.TESTREPORTS TR
ON TR.LABREPORTID = LR.LABREPORTID
WHERE RR.PATIENTSERVICE = 'BLOODBANK'
AND RR.LRN = V_LRN
AND RT.TESTID = V_ISSEROTESTSID
AND UPPER(TR.RESULT)= V_ISSEROTESTVALUES
AND RR.UHID = IV_UHID
AND RR.LOCATIONID = IV_LOCATION;","[('join', 'Common/Statement/Pre/join.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
17,"Raise Notice '%',V_COUNT;","Raise Notice '%',V_COUNT;","[('raisenotice', 'Common/Statement/Pre/raisenotice.py')]","[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
18,END LOOP;,END LOOP;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
19,"IF V_COUNT > 0 THEN
OV_RESULT := 'Y';","IF V_COUNT > 0 THEN
OV_RESULT := 'Y';",[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
20,"ELSE
OV_RESULT := 'N';","ELSE
OV_RESULT := 'N';",[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
21,END IF;,END IF;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
22,END IF;,END IF;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
23,END;,END;,[],"[('exception_commenting', 'Procedure/Post/exception_commenting.py'), ('record', 'Procedure/Post/record.py'), ('strict', 'Procedure/Post/strict.py'), ('sub_query_alias', 'Common/Post/sub_query_alias.py'), ('exception_variable_commenting', 'Common/Post/exception_variable_commenting.py'), ('input_parameters_issue', 'Common/Post/input_parameters_issue.py')]"
