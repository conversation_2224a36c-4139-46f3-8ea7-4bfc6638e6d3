CREATE TABLE temp (
        col0 INT GENERATED ALWAYS AS IDENTITY (START WITH 100 INCREMENT BY 5),
        col00 INT GENERATED ALWAYS AS IDENTITY (START WITH 100 INCREMENT BY 5),
        col1 numeric,
        col2 numeric(20),
        col5 varchar(20),
        col6 varchar(20),
        col7 text,
        col8 bytea,
        col9 UUID,
        col9_1 UUID default gen_random_uuid() ,
        col10 varchar(20),
        col11 timestamp,
        col12 timestamp DEFAULT LOCALTIMESTAMP,
        col13 timestamp DEFAULT CURRENT_TIMESTAMP,
        col14 boolean DEFAULT 't',
        col15 text,
        col17 double precision,
        col18 bytea,
        col19 text,
        col20 char(10),
        col21 char(10),
        col22 double precision,
        col23 double precision
) ;