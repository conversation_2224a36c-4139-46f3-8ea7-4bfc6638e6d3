create or replace procedure "ambb"."p_rptnoofpicksloc" () 
language  plpgsql           as 
 $body$  begin
    select TRUNC(sysdate) , trunc(sysdate +1) , trunc(sysdate - 1), trunc(sysdate - 2) from  
    dual;
    TRUNC(add_months(Last_day(sysdate) + 1, -2)) AND  TRUNC(add_months(last_day(sysdate), -1)    );

    TRUNC(add_months(Last_day(sysdate) + 1, -2)) AND  TRUNC(add_months(last_day(sysdate), -1)) AND X.LUXURYTAX>0

    TRUNC(add_months(Last_day(sysdate) + 1, -2)) AND  TRUNC(add_months(last_day(sysdate), -1)) UNION ALL
     
end;
$body$;