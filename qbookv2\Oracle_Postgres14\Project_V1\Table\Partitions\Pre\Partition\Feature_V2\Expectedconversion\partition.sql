CREATE TABLE billing.patientbilldetails (
               patientservicerequistid numeric NULL,
               servicename varchar(1000) NULL,
               serviceid numeric NULL,
               individualrate numeric NULL,
               createdby varchar(50) NULL,
               createddate date NULL,
               updatedby varchar(50) NULL,
               updateddate date NULL,
               billno varchar(100) NULL,
               servicequantity numeric NULL,
               incomeamount numeric NULL,
               servicetypeid numeric NULL,
               servicerequesteddate date NULL,
               bedcategoryid numeric NULL,
               deptid varchar(50) NULL,
               doctoramount numeric NULL,
               doctorid numeric NULL,
               individualratefx numeric NULL,
               extrachargeflag varchar(5) NULL,
               patientoccupancy bpchar(1) NULL,
               startdatetime date NULL,
               enddatetime date NULL,
               creditdiscountpercentage numeric NULL,
               supplimantarybillno varchar(100) NULL,
               reftariff numeric NULL,
               remarks varchar(350) NULL,
               servicerequestdetailsid numeric NULL,
               requestid numeric NULL,
               patientpayable bpchar(1) NULL,
               bedsideflag varchar(5) NULL,
               bedcode varchar(100) NULL,
               reftarifffx numeric NULL,
               status numeric NULL,
               transferlocationid varchar(100) NULL,
               basederivedrelation numeric NULL,
               requestno_new varchar(200) NULL
)
PARTITION BY RANGE (patientservicerequistid);

create table patientbilldetails_3  partition of patientbilldetails for values from (1) to (3000000);
create table patientbilldetails_4  partition of patientbilldetails for values from (3000000) to (3100000);
create table patientbilldetails_5  partition of patientbilldetails for values from (3100000) to (9000000);
create table patientbilldetails_6  partition of patientbilldetails for values from (9000000) to (12000000);
create table patientbilldetails_7  partition of patientbilldetails for values from (12000000) to (15000000);
create table patientbilldetails_8  partition of patientbilldetails for values from (15000000) to (16000000);
create table patientbilldetails_9  partition of patientbilldetails for values from (16000000) to (17000000);
create table patientbilldetails_10 partition of patientbilldetails for values from (17000000) to (18000000);
create table patientbilldetails_11 partition of patientbilldetails for values from (18000000) to (19000000);
create table patientbilldetails_12 partition of patientbilldetails for values from (19000000) to (20000000);
create table patientbilldetails_13 partition of patientbilldetails for values from (20000000) to (21000000);
create table patientbilldetails_14 partition of patientbilldetails for values from (21000000) to (22000000);
create table patientbilldetails_15 partition of patientbilldetails for values from (22000000) to (23000000);
create table patientbilldetails_18 partition of patientbilldetails for values from (23000000) to (24000000);
create table patientbilldetails_19 partition of patientbilldetails for values from (24000000) to (25000000);
create table patientbilldetails_20 partition of patientbilldetails for values from (25000000) to (26000000);
create table patientbilldetails_21 partition of patientbilldetails for values from (26000000) to (27000000);
create table patientbilldetails_22 partition of patientbilldetails for values from (27000000) to (28000000);
create table patientbilldetails_23 partition of patientbilldetails for values from (28000000) to (29000000);
create table patientbilldetails_24 partition of patientbilldetails for values from (29000000) to (30000000);
create table patientbilldetails_25 partition of patientbilldetails for values from (30000000) to (31000000);
create table patientbilldetails_26 partition of patientbilldetails for values from (31000000) to (32000000);
create table patientbilldetails_27 partition of patientbilldetails for values from (32000000) to (33000000);
create table patientbilldetails_28 partition of patientbilldetails for values from (33000000) to (34000000);
create table patientbilldetails_29 partition of patientbilldetails for values from (34000000) to (35000000);
create table patientbilldetails_30 partition of patientbilldetails for values from (35000000) to (36000000);
create table patientbilldetails_31 partition of patientbilldetails for values from (36000000) to (37000000);
create table patientbilldetails_32 partition of patientbilldetails for values from (37000000) to (38000000);
create table patientbilldetails_33 partition of patientbilldetails for values from (38000000) to (39000000);
create table patientbilldetails_34 partition of patientbilldetails for values from (39000000) to (40000000);
create table patientbilldetails_35 partition of patientbilldetails for values from (40000000) to (41000000);
create table patientbilldetails_36 partition of patientbilldetails for values from (41000000) to (42000000);
create table patientbilldetails_37 partition of patientbilldetails for values from (42000000) to (43000000);
create table patientbilldetails_38 partition of patientbilldetails for values from (43000000) to (44000000);
create table patientbilldetails_39 partition of patientbilldetails for values from (44000000) to (45000000);


CREATE TABLE sales_by_region (item INTEGER, qty INTEGER, 
             store_name VARCHAR(30), state_code VARCHAR(2),
             sale_date DATE) 
     PARTITION BY LIST (state_code) 
          
    create table region_east   partition of  sales_by_region
       for  values in  ('MA','NY','CT','NH','ME','MD','VA','PA','NJ');
    create table   region_west partition of sales_by_region
      for values in  ('CA','AZ','NM','OR','WA','UT','NV','CO');
   create table region_south  PARTITION of sales_by_region
        for VALUES in ('TX','KY','TN','LA','MS','AR','AL','GA');
    create table region_central PARTITION  of sales_by_region 
       for  VALUES in ('OH','ND','SD','MO','IL','MI','IA');
    create table region_null partition of  sales_by_region
       for values in (NULL);
      create table region_unknown PARTITION  of sales_by_region
        for values in  (default);