migration_name,schema_name,object_name,object_type,tgt_object_id,source_statement_number,target_statement_number,original_source_statement,original_target_statement,ai_converted_statement,original_deployment_error
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,3,iv_row ROW_LIST;,"-- TYPE aicctest.CRM.MESSAGETRANS%ROWTYPE[] IS TABLE of aicctest.CRM.MESSAGETRANS%ROWTYPE INDEX BY integer;
    iv_row aicctest.CRM.MESSAGETRANS%ROWTYPE[];","iv_row_errortext character varying ( 2000 )[] ; 
iv_row_fromempid character varying ( 100 )[] ; 
iv_row_messagetype character varying ( 100 )[] ; 
iv_row_toaddress character varying ( 500 )[] ; 
iv_row_sourcetype character varying ( 500 )[] ; 
iv_row_errorflag numeric[] ; 
iv_row_sourceid numeric[] ; 
iv_row_toempid character varying ( 100 )[] ; 
iv_row_messagetext character varying ( 2000 )[] ; 
iv_row_messagechannel character varying ( 200 )[] ; ","syntax error at or near "")""
LINE 67: ...('/document/LOCATION_ID/text(',v_DocInput)))::text)::text=''...
                                                              ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,11,iv_row(i).FROMEMPID       := IXML_MESSAGE.extract('/MessageTransEntity/FromEmployeeId/text()').GetStringVal();,"iv_row (i).FROMEMPID := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/MessageTransEntity/FromEmployeeId/text()', IXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/FromEmployeeId/text()', IXML_MESSAGE)))
            END)::text;","iv_row_fromempid[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/FromEmployeeId/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/FromEmployeeId/text()', IXML_MESSAGE))) END)::text;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 35: iv_row (i).FROMEMPID := (
         ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,12,iv_row(i).TOEMPID         := IXML_MESSAGE.extract('/MessageTransEntity/ToEmployeeId/text()').GetStringVal();,"iv_row (i).TOEMPID := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/MessageTransEntity/ToEmployeeId/text()', IXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/ToEmployeeId/text()', IXML_MESSAGE)))
            END)::text;","iv_row_toempid[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/ToEmployeeId/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/ToEmployeeId/text()', IXML_MESSAGE))) END)::text;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 45: iv_row (i).TOEMPID := (
         ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,13,iv_row(i).MessageType     := IXML_MESSAGE.extract('/MessageTransEntity/MessageType/text()').GetStringVal();,"iv_row (i).MessageType := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/MessageTransEntity/MessageType/text()', IXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/MessageType/text()', IXML_MESSAGE)))
            END)::text;","iv_row_messagetype[i] :=
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/MessageType/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/MessageType/text()', IXML_MESSAGE))) END)::text;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 55: iv_row (i).MessageType := (
         ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,14,iv_row(i).MessageText     := IXML_MESSAGE.extract('/MessageTransEntity/MessageText/text()').GetStringVal();,"iv_row (i).MessageText := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/MessageTransEntity/MessageText/text()', IXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/MessageText/text()', IXML_MESSAGE)))
            END)::text;","iv_row_messagetext[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/MessageText/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/MessageText/text()', IXML_MESSAGE))) END)::text;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 65: iv_row (i).MessageText := (
         ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,15,iv_row(i).MessageChannel  := IXML_MESSAGE.extract('/MessageTransEntity/MessageChannel/text()').GetStringVal();,"iv_row (i).MessageChannel := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/MessageTransEntity/MessageChannel/text()', IXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/MessageChannel/text()', IXML_MESSAGE)))
            END)::text;","iv_row_messagechannel[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/MessageChannel/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/MessageChannel/text()', IXML_MESSAGE))) END)::text;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 75: iv_row (i).MessageChannel := (
         ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,16,iv_row(i).SourceType      := IXML_MESSAGE.extract('/MessageTransEntity/SourceType/text()').GetStringVal();,"iv_row (i).SourceType := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/MessageTransEntity/SourceType/text()', IXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/SourceType/text()', IXML_MESSAGE)))
            END)::text;","iv_row_sourcetype[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/SourceType/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/SourceType/text()', IXML_MESSAGE))) END)::text;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 85: iv_row (i).SourceType := (
         ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,17,iv_row(i).SourceID        := IXML_MESSAGE.extract('/MessageTransEntity/SourceId/text()').GetNumberVal();,"iv_row (i).SourceID := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/MessageTransEntity/SourceId/text()', IXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/SourceId/text()', IXML_MESSAGE)))::text
            END)::numeric;","iv_row_sourceid[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/SourceId/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/SourceId/text()', IXML_MESSAGE))) END)::numeric;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 95: iv_row (i).SourceID := (
         ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,18,"-- TO EXTRACT OPTIONAL PARAMETERS -------------
    IF ( iXML_MESSAGE.extract('/MessageTransEntity/ToAddress/text()') IS NULL ) THEN
     iv_row(i).ToAddress       := NULL;","-- TO EXTRACT OPTIONAL PARAMETERS
        --
        IF ((
            CASE WHEN (
            SELECT
                unnest(xpath('/MessageTransEntity/ToAddress/text()', iXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/ToAddress/text()', iXML_MESSAGE)))::text
            END) IS NULL) THEN
iv_row (i).ToAddress := NULL;","-- TO EXTRACT OPTIONAL PARAMETERS -------------
IF ((CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/ToAddress/text()', iXML_MESSAGE)))::text = '' THEN NULL
ELSE (SELECT unnest(xpath('/MessageTransEntity/ToAddress/text()', iXML_MESSAGE)))::text END) IS NULL) THEN
 iv_row_toaddress[i] := null ; 
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 117: iv_row (i).ToAddress := NULL;
          ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,19,"-- TO EXTRACT OPTIONAL PARAMETERS -------------
    IF ( iXML_MESSAGE.extract('/MessageTransEntity/ToAddress/text()') IS NULL ) THEN
     iv_row(i).ToAddress       := NULL;","ELSE
            iv_row (i).ToAddress := (
                CASE WHEN (
                    SELECT
                        unnest(xpath('/MessageTransEntity/ToAddress/text()', IXML_MESSAGE)))::text = '' THEN
                    NULL
                ELSE
                    (
                        SELECT
                            unnest(xpath('/MessageTransEntity/ToAddress/text()', IXML_MESSAGE)))
                END)::text;","else iv_row_toaddress[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/ToAddress/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/ToAddress/text()', IXML_MESSAGE))) END)::text;","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 119:             iv_row (i).ToAddress := (
                      ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,21,"IF ( iXML_MESSAGE.extract('/MessageTransEntity/ErrorText/text()') IS NULL ) THEN
     iv_row(i).ErrorText       := NULL;","IF ((
            CASE WHEN (
            SELECT
                unnest(xpath('/MessageTransEntity/ErrorText/text()', iXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/ErrorText/text()', iXML_MESSAGE)))::text
            END) IS NULL) THEN
iv_row (i).ErrorText := NULL;","IF ((CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/ErrorText/text()', iXML_MESSAGE)))::text = '' THEN NULL
ELSE (SELECT unnest(xpath('/MessageTransEntity/ErrorText/text()', iXML_MESSAGE)))::text END) IS NULL)  
then iv_row_errortext[i] := null ; 
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 140: iv_row (i).ErrorText := NULL;
          ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,22,"IF ( iXML_MESSAGE.extract('/MessageTransEntity/ErrorText/text()') IS NULL ) THEN
     iv_row(i).ErrorText       := NULL;","ELSE
            iv_row (i).ErrorText := (
                CASE WHEN (
                    SELECT
                        unnest(xpath('/MessageTransEntity/ErrorText/text()', IXML_MESSAGE)))::text = '' THEN
                    NULL
                ELSE
                    (
                        SELECT
                            unnest(xpath('/MessageTransEntity/ErrorText/text()', IXML_MESSAGE)))
                END)::text;","else iv_row_errortext[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/ErrorText/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/ErrorText/text()', IXML_MESSAGE))) END)::text;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 142:             iv_row (i).ErrorText := (
                      ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,24,"IF ( iXML_MESSAGE.extract('/MessageTransEntity/ErrorFlag/text()') IS NULL ) THEN
     iv_row(i).ErrorFlag       := 0;","IF ((
            CASE WHEN (
            SELECT
                unnest(xpath('/MessageTransEntity/ErrorFlag/text()', iXML_MESSAGE)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/MessageTransEntity/ErrorFlag/text()', iXML_MESSAGE)))::text
            END) IS NULL) THEN
iv_row (i).ErrorFlag := 0;","IF ((CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/ErrorFlag/text()', iXML_MESSAGE)))::text = '' THEN NULL
ELSE (SELECT unnest(xpath('/MessageTransEntity/ErrorFlag/text()', iXML_MESSAGE)))::text END) IS NULL) 
 then iv_row_errorflag[i] := 0 ; 
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 163: iv_row (i).ErrorFlag := 0;
          ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,25,"IF ( iXML_MESSAGE.extract('/MessageTransEntity/ErrorFlag/text()') IS NULL ) THEN
     iv_row(i).ErrorFlag       := 0;","ELSE
            iv_row (i).ErrorFlag := (
                CASE WHEN (
                    SELECT
                        unnest(xpath('/MessageTransEntity/ErrorFlag/text()', IXML_MESSAGE)))::text = '' THEN
                    NULL
                ELSE
                    (
                        SELECT
                            unnest(xpath('/MessageTransEntity/ErrorFlag/text()', IXML_MESSAGE)))
                END)::text;","else iv_row_errorflag[i] := 
 (CASE WHEN (SELECT unnest(xpath('/MessageTransEntity/ErrorFlag/text()', IXML_MESSAGE)))::text = '' THEN NULL
  ELSE (SELECT unnest(xpath('/MessageTransEntity/ErrorFlag/text()', IXML_MESSAGE))) END)::numeric;
","Postgres14 Error: ERROR:  syntax error at or near ""iv_row""
LINE 165:             iv_row (i).ErrorFlag := (
                      ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,27,"-------------------------------------------------

     IF UPPER(iv_row(i).MessageChannel) = 'DASHBOARD' THEN
      V_SENT := 1;","--
        IF UPPER(iv_row (i).MessageChannel) = 'DASHBOARD' THEN
            V_SENT := 1;","--
if upper ( iv_row_messagechannel[i] )= 'DASHBOARD' then
 v_sent := 1 ; ","Postgres14 Error: ERROR:  syntax error at or near "".""
LINE 177:         IF UPPER(iv_row (i).MessageChannel) = 'DASHBOARD' TH...
                                     ^
"
Oracle_Postgres14,AICCTEST,P_ADDMESSAGETRANS,Procedure,194413,,30,"INSERT INTO CRM.MESSAGETRANS
     (MESSAGETRANSID,FROMEMPID,TOEMPID,MESSAGETYPE,MESSAGETEXT,MESSAGECHANNEL,TOADDRESS,SENTFLAG,ACTIVEFLAG,
     SOURCETYPE,SOURCEID,ERRORTEXT,ERRORFLAG,
     CREATEDBY,CREATEDON )

     VALUES( CRM.S_WF_MESSAGETRANSID.NEXTVAL,iv_row(i).FROMEMPID,iv_row(i).TOEMPID,iv_row(i).MessageType,
             iv_row(i).MessageText,iv_row(i).MessageChannel,iv_row(i).ToAddress,V_SENT,1,
             iv_row(i).SourceType,iv_row(i).SourceID, iv_row(i).ErrorText,iv_row(i).ErrorFlag,
       Iv_LoginID,sysdate);","INSERT INTO CRM.MESSAGETRANS (MESSAGETRANSID, FROMEMPID, TOEMPID, MESSAGETYPE, MESSAGETEXT, MESSAGECHANNEL, TOADDRESS, SENTFLAG, ACTIVEFLAG, SOURCETYPE, SOURCEID, ERRORTEXT, ERRORFLAG, CREATEDBY, CREATEDON)
            VALUES (nextval('CRM.S_WF_MESSAGETRANSID'), iv_row (i).FROMEMPID, iv_row (i).TOEMPID, iv_row (i).MessageType, iv_row (i).MessageText, iv_row (i).MessageChannel, iv_row (i).ToAddress, V_SENT, 1, iv_row (i).SourceType, iv_row (i).SourceID, iv_row (i).ErrorText, iv_row (i).ErrorFlag, Iv_LoginID, current_timestamp(0)::timestamp);","insert into messagetrans ( messagetransid, fromempid, toempid, messagetype, messagetext, messagechannel, toaddress, sentflag, activeflag, sourcetype, sourceid, errortext, errorflag, createdby, createdon )
 values (nextval('CRM.S_WF_MESSAGETRANSID'),iv_row_fromempid[i],iv_row_toempid[i],iv_row_messagetype[i], iv_row_messagetext[i],iv_row_messagechannel[i],iv_row_toaddress[i],v_sent,1, iv_row_sourcetype[i],iv_row_sourceid[i], iv_row_errortext[i],iv_row_errorflag[i], iv_loginid,current_timestamp(0)::timestamp ) ; 
","Postgres14 Error: ERROR:  syntax error at or near "".""
LINE 183: ...ES (nextval('CRM.S_WF_MESSAGETRANSID'), iv_row (i).FROMEMPID...
                                                               ^
"
