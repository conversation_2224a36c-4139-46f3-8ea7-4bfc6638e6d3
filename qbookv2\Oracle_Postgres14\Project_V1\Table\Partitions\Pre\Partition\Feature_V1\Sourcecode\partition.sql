CREATE TABLE "PATIENTBILLDETAILS" 
   (	"PATIENTSERVICEREQUISTID" NUMBER NOT NULL ENABLE, 
	"SERVICENAME" VARCHAR2(1000), 
	"SERVICEID" NUMBER, 
	"INDIVIDUALRATE" NUMBER, 
	"CREATEDBY" VARCHAR2(50), 
	"CREATEDDATE" DATE, 
	"UPDATEDBY" VARCHAR2(50), 
	"UPDATEDDATE" DATE, 
	"BILLNO" VARCHAR2(100), 
	"SERVICEQUANTITY" NUMBER, 
	"INCOMEAMOUNT" NUMBER, 
	"SERVICETYPEID" NUMBER, 
	"SERVICEREQUESTEDDATE" DATE, 
	"BEDCATEGORYID" NUMBER, 
	"DEPTID" VARCHAR2(50), 
	"DOCTORAMOUNT" NUMBER, 
	"DOCTORID" NUMBER, 
	"INDIVIDUALRATEFX" NUMBER, 
	"EXTRACHARGEFLAG" VARCHAR2(5), 
	"PATIENTOCCUPANCY" CHAR(1), 
	"STARTDATETIME" DATE, 
	"ENDDATETIME" DATE, 
	"CREDITDISCOUNTPERCENTAGE" NUMBER, 
	"SUPPLIMANTARYBILLNO" VARCHAR2(100), 
	"REFTARIFF" NUMBER, 
	"REMARKS" VARCHAR2(350), 
	"SERVICEREQUESTDETAILSID" NUMBER, 
	"REQUESTID" NUMBER, 
	"PATIENTPAYABLE" CHAR(1), 
	"BEDSIDEFLAG" VARCHAR2(5), 
	"BEDCODE" VARCHAR2(100), 
	"REFTARIFFFX" NUMBER, 
	"STATUS" NUMBER, 
	"TRANSFERLOCATIONID" VARCHAR2(100), 
	"BASEDERIVEDRELATION" NUMBER, 
	"REQUESTNO_NEW" VARCHAR2(200), 
	"GST_FLAG" CHAR(1), 
	"CGST_TARIFF" NUMBER, 
	"SGST_TARIFF" NUMBER, 
	"CGST_PERCENT" NUMBER, 
	"SGST_PERCENT" NUMBER, 
	"TOTAL_GST_PERCENT" NUMBER, 
	"DISCOUNTAMOUNT" NUMBER, 
	"DISCOUNTPERCENT" NUMBER, 
	"AMOUNTAFTER_DISCOUNT" NUMBER, 
	 CONSTRAINT "PATIENTSERVICEREQUEST_PK" PRIMARY KEY ("PATIENTSERVICEREQUISTID")
  USING INDEX  ENABLE, 
	 CONSTRAINT "PATIENTBILLDETAILS_FK" FOREIGN KEY ("BILLNO")
	  REFERENCES "PATIENTBILL" ("BILLNO") ON DELETE CASCADE ENABLE
   ) 
  PARTITION BY RANGE ("PATIENTSERVICEREQUISTID") INTERVAL (1000000) 
 (PARTITION "PATIENTBILLDETAILS_3"  VALUES LESS THAN (3000000) , 
 PARTITION "PATIENTBILLDETAILS_4"  VALUES LESS THAN ('3100000') , 
 PARTITION "PATIENTBILLDETAILS_5"  VALUES LESS THAN ('9000000') , 
 PARTITION "PATIENTBILLDETAILS_6"  VALUES LESS THAN (12000000) , 
 PARTITION "PATIENTBILLDETAILS_7"  VALUES LESS THAN (15000000) , 
 PARTITION "SYS_P61"  VALUES LESS THAN (16000000) , 
 PARTITION "SYS_P101"  VALUES LESS THAN (17000000) , 
 PARTITION "SYS_P121"  VALUES LESS THAN (18000000) , 
 PARTITION "SYS_P141"  VALUES LESS THAN (19000000) , 
 PARTITION "SYS_P161"  VALUES LESS THAN (20000000) , 
 PARTITION "SYS_P181"  VALUES LESS THAN (21000000) , 
 PARTITION "SYS_P221"  VALUES LESS THAN (22000000) , 
 PARTITION "SYS_P241"  VALUES LESS THAN (23000000) , 
 PARTITION "SYS_P261"  VALUES LESS THAN (24000000) , 
 PARTITION "SYS_P281"  VALUES LESS THAN (25000000) , 
 PARTITION "SYS_P301"  VALUES LESS THAN (26000000) , 
 PARTITION "SYS_P341"  VALUES LESS THAN (27000000) , 
 PARTITION "SYS_P361"  VALUES LESS THAN (28000000) , 
 PARTITION "SYS_P381"  VALUES LESS THAN (29000000) , 
 PARTITION "SYS_P401"  VALUES LESS THAN (30000000) , 
 PARTITION "SYS_P421"  VALUES LESS THAN (31000000) , 
 PARTITION "SYS_P461"  VALUES LESS THAN (32000000) , 
 PARTITION "SYS_P601"  VALUES LESS THAN (33000000) , 
 PARTITION "SYS_P824"  VALUES LESS THAN (34000000) , 
 PARTITION "SYS_P1061"  VALUES LESS THAN (35000000) , 
 PARTITION "SYS_P1301"  VALUES LESS THAN (36000000) , 
 PARTITION "SYS_P1621"  VALUES LESS THAN (37000000) , 
 PARTITION "SYS_P2001"  VALUES LESS THAN (38000000) , 
 PARTITION "SYS_P2361"  VALUES LESS THAN (39000000) , 
 PARTITION "SYS_P2721"  VALUES LESS THAN (40000000) , 
 PARTITION "SYS_P3081"  VALUES LESS THAN (41000000) , 
 PARTITION "SYS_P3481"  VALUES LESS THAN (42000000) , 
 PARTITION "SYS_P3841"  VALUES LESS THAN (43000000) , 
 PARTITION "SYS_P4241"  VALUES LESS THAN (44000000) , 
 PARTITION "SYS_P4621"  VALUES LESS THAN (45000000) ) ;


CREATE TABLE sales_by_region (item# INTEGER, qty INTEGER, 
             store_name VARCHAR(30), state_code VARCHAR(2),
             sale_date DATE) 
     PARTITION BY LIST (state_code) 
     (
     PARTITION region_east
        VALUES ('MA','NY','CT','NH','ME','MD','VA','PA','NJ'),
     PARTITION region_west
        VALUES ('CA','AZ','NM','OR','WA','UT','NV','CO'),
     PARTITION region_south
        VALUES ('TX','KY','TN','LA','MS','AR','AL','GA'),
     PARTITION region_central 
        VALUES ('OH','ND','SD','MO','IL','MI','IA'),
     PARTITION region_null
        VALUES (NULL),
     PARTITION region_unknown
        VALUES (DEFAULT)
     );