Create table temp
(col0 number GENERATED ALWAYS AS IDENTITY(START WITH 100, INCREMENT BY 5),
col00 int GENERATED ALWAYS AS IDENTITY(START WITH 100, INCREMENT BY 5),
col1 int,
col2 number(20),
col3 number(10,2),
col4 number,
col5 varchar2(20),
col6 varchar(20),
col7 clob,
col8 blob,
col9 GUID default sys_guid,
col10 nvarchar(20),
col11 date,
col12 timestamp default sysdate,
col13 timestamp(6) default systimestamp,
col14  boolean default true,
col15  LONG,
col17 FLOAT,
col18  BFILE ,
col19  LONG RAW,
col20  char(10),
col21  nchar(10),
col22  BINARY_FLOAT,
col23  BINARY_DOUBLE
);