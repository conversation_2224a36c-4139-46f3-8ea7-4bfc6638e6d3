create or replace procedure "ambb"."p_rptnoofpicksloc" ()
language  plpgsql           as
 $body$  begin
    select date_trunc('day',sysdate) , date_trunc('day',sysdate +1) , date_trunc('day',sysdate - 1), date_trunc('day',sysdate - 2) from
    dual;
    date_trunc('day',add_months(Last_day(sysdate) + 1, -2)) AND date_trunc('day',add_months(last_day(sysdate), -1));

    date_trunc('day',add_months(Last_day(sysdate) + 1, -2)) AND date_trunc('day',add_months(last_day(sysdate), -1)) AND X.LUXURYTAX>0

    date_trunc('day',add_months(Last_day(sysdate) + 1, -2)) AND date_trunc('day',add_months(last_day(sysdate), -1)) UNION ALL

end;
$body$;