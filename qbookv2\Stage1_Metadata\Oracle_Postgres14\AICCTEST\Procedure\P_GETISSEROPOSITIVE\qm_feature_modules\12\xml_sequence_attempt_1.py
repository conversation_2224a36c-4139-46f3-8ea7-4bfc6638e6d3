import re

def xml_sequence(data, schema):
    select_pattern = re.findall(r'\bSELECT\b.*?;', data,
                                flags=re.IGNORECASE | re.DOTALL)
    for select_query in select_pattern:

        check = re.search(r'\s*Table\s*\(\s*xmlsequence\s*\(', select_query, flags=re.DOTALL | re.I)
        if check:
            from_table = re.findall(r'\s*table\s*\(.*?;', select_query, flags=re.DOTALL | re.I)
            for from_tab in from_table:
                from_manp = re.sub(r'\btable\s*\(\s*xmlsequence\s*\(', '(with ctc as ', from_tab, flags=re.DOTALL | re.I)
                from_manp = re.sub(r'(?:\:\s*\:\s*text|:\:\s*\:\s*numeric)', '', from_manp,
                                   flags=re.DOTALL | re.I)
                check_wrd = re.search(r'\,\s*\w+\s*\(', from_manp, flags=re.DOTALL | re.I)
                if not check_wrd:
                    xml_stmt = re.findall(r'\(\s*select\s*unnest\s*\(\s*xpath\s*\(.*?\)\s*\)\s*\)', from_manp, flags=re.DOTALL | re.I)
                    for xml in xml_stmt:
                        if xml.count('(') > xml.count(')'):
                            xml1 = re.sub(r'\)\s*\)\s*\)', ')) as value ) select value from ctc )', xml, flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                        else:
                            xml1 = re.sub(r'\)\s*\)\s*\)', ')) as value ) select value from ctc ', xml,
                                          flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                            from_manp = re.sub(r'\bctc\s*\)\s*\)', 'ctc )', from_manp, flags=re.DOTALL | re.I)
                            data = data.replace(from_tab, from_manp)
                if check_wrd:
                    xml_stmt = re.findall(r'\(\s*select\s*unnest\s*\(\s*xpath\s*\(.*?\)\s*\)\s*\)\)', from_manp,
                                          flags=re.DOTALL | re.I)
                    for xml in xml_stmt:
                        if xml.count('(') > xml.count(')'):
                            xml1 = re.sub(r'\)\s*\)\s*\)\s*\)', '))) as value ) select value from ctc ', xml,
                                          flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                        else:
                            xml1 = re.sub(r'\)\s*\)\s*\)\s*\)', '))) as value ) select value from ctc ', xml,
                                          flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                            from_manp = re.sub(r'\bctc\s*\)\s*\)', 'ctc )', from_manp, flags=re.DOTALL | re.I)
                            data = data.replace(from_tab, from_manp)

    return data