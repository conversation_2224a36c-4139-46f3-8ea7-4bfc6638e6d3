/*@
"wards.p_addadmissiondetails(<?xml version=1.0"" encoding=""utf-16""?>
<AdmissionNoteRequest xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
  <AdmissionNote Admissionnumber=""0"" Registrationnumber=""0"" UHID=""
  ACH1.**********"" MLCCheck=""30"" CTCCheck=""30"" Medicallegalcase=""0"" STRENGTH="" 
   Clinicaltrial="""" MLCnumber="""" Doctorname=""720138"" FilePath=""""
    FileName="""" FileType="""" Specialization=""34"" Priority=""31""
    Expecteddateofadmission=""2022-09-08T00:00:00"" Expecteddateofdischarge=""1900-01-01T00:00:00""
    Expecteddurationofstay=""1""
   DurationType=""208"" Recommendedwards=""1291"" CancelBy="""" CancelReason=""""
  Canceldate=""1900-01-01T00:00:00"" ExpiryDate=""2022-09-12T00:00:00"" Comments=""test""
   Infectious=""44"" Isolationrequired=""44"" LocationID=""10551"" IsolationComments=""""
    Daycare="""" Estimatedcostoftreatment=""500"" Admissionnotepath="""" IPNO=""""
    BedLocation=""10551"" RecommendedBedCatogery=""492"" InfectiousType=""0""
     AmountType=""0"" Amount="""" Status=""0"" CreatedBy=""921186""
     Createddate=""2022-09-02T00:00:00"" BillingStatus=""0"" DietStatus="""" Vulnerable=""44""
      VulnerableComments="""" OPNumber="""" ClinicalNotes="""" BillingType=""0"" Unit=""0""
      UnitHead=""0"" AttenderName="""" RelationwithPatient=""0"">
    <Findings />
    <Diagnosis><Diagnosis ICDCode=""477327016""
    DiagnosisName=""477327016-A - alphalipoproteinaemia neuropathy""
    Type=""Pp"" Remarks="""" /></Diagnosis>
    <Surgery />
    <Prescription />
    <Instruction />
    <VitalInstruction />
    <Specialnotes />
    <CrossReferralDetailsXml><Crossreferral Doctor=""717971""
    Speciality=""59"" Referfor=""2"" /><Crossreferral Doctor=""718967""
     Speciality=""8"" Referfor=""2"" /></CrossReferralDetailsXml>
  </AdmissionNote>
  <ActionRequest>
    <Target />
    <Identifier>672cdb7c-fcad-4ac1-a641-2261e392baa2</Identifier>
    <Action>ADDADMISSIONDETAILS</Action>
  </ActionRequest>
</AdmissionNoteRequest>,921186,out parameter,out parameter)""	"

@*/

create or replace procedure wards.p_addadmissiondetails(iclob_admissiondetails text, iv_loginid character varying, inout on_returncode numeric, inout v_admissionno numeric)
 language plpgsql
as $procedure$ 

 begin
 set search_path to wards ;

 on_returncode := -1 ;

 
  v_admissiondetails := xml(iclob_admissiondetails ) ;


 
 v_locationid :=(select unnest(xpath('/admissionnoterequest/admissionnote/@locationid',v_admissiondetails))::text) ;

 
 v_createdby :=(select unnest(xpath('/admissionnoterequest/admissionnote/@createdby',v_admissiondetails))::text) ;

 v_uhid := upper(
 
 (select unnest(xpath('/admissionnoterequest/admissionnote/@uhid',v_admissiondetails))::text)
 ) ;


 
 v_doctorname := (select unnest(xpath('/admissionnoterequest/admissionnote/@doctorname',v_admissiondetails))::text) ;

 
 v_clinicalnotes :=(select unnest(xpath('/admissionnoterequest/admissionnote/@clinicalnotes',v_admissiondetails))::text) ;

 if v_doctorname = v_createdby then v_regularizationflag := 1 ;

 else v_regularizationflag := 0 ;

 end if ;

 select nextval('wards.s_admissionno' ) into strict v_admissionno  ;

 insert into admissiondetails(admissionno, registrationno, uhid, medicallegalcase, clinicaltrial,
	mlcnumber, doctorname, specialization, priority, expecteddateofadmission, expecteddurationofstay,
	durationtype, recommendedwards, comments, infectious, isolationrequired, findings, locationid, daycare,
	specialnotes, estimatedcostoftreatment, amounttype, status, createdby, createddate, admissionstatus, credittype,
	bedcategory, transactionalstatus, bedlocation, isolationcomments, filetype, filename, admissionnotepath,
	admissionexpirydate, mlcflag, ctcflag, regularizationflag, vulnerable, vulnerablecomments, opnumber, clinicalnotes,
	primarydoctor, billtype, unit, unithead, attendantname, relationshipwithpatient ) values (v_admissionno,
	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@registrationnumber',v_admissiondetails))::numeric),
	upper(
	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@uhid',v_admissiondetails))::text)
	),
	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@medicallegalcase',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@clinicaltrial',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@mlcnumber',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@doctorname',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@specialization',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@priority',v_admissiondetails))::numeric),

	to_date(
	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@expecteddateofadmission',v_admissiondetails))::text),
	'yyyy-mm-dd"t"hh24:mi:ss' ),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@expecteddurationofstay',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@durationtype',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@recommendedwards',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@comments',v_admissiondetails))::text),

	
    (select unnest(xpath('/admissionnoterequest/admissionnote/@infectious',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@isolationrequired',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/findings',v_admissiondetails))::text),v_locationid,

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@daycare',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/specialnotes',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@estimatedcostoftreatment',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@amounttype',v_admissiondetails))::text),

	1, v_createdby, clock_timestamp( ), 2,

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@amount',v_admissiondetails))),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@recommendedbedcatogery',v_admissiondetails))),

	0,
	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@bedlocation',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@isolationcomments',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@filetype',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@filename',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@admissionnotepath',v_admissiondetails))::text),

	to_date(
	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@expirydate',v_admissiondetails))::text),
	'yyyy-mm-dd"t"hh24:mi:ss' ) ,

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@mlccheck',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@ctccheck',v_admissiondetails))::numeric),
	 v_regularizationflag,

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@vulnerable',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@vulnerablecomments',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@opnumber',v_admissiondetails))::text),
	v_clinicalnotes,

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@doctorname',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@billingtype',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@unit',v_admissiondetails))::text),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@unithead',v_admissiondetails))::numeric),

	
	(select unnest(xpath('/admissionnoterequest/admissionnote/@attendername',v_admissiondetails))::text),

	
    (select unnest(xpath('/admissionnoterequest/admissionnote/@relationwithpatient',v_admissiondetails))::numeric)
	) ;


 
 end ;

 $procedure$
;