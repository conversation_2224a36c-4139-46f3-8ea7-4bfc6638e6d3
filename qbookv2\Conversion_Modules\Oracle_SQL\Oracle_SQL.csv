Migration_Name,Feature_Name,Keywords,Predecessor,Estimations,Automation_Percentage,Object_Path
Oracle_SQL,Datatypes,bfile|binary_float|binary_double|blob|date|timestamp|nchar|raw|urowid|number|clob|varchar2|date|timestamp|char|sysdate,No Predecessor,1,100,Table/Pre
Oracle_SQL,Column,Null,No Predecessor,20,60,Column/Pre
Oracle_SQL,Function_handling,function,No Predecessor,60,70,Function/Pre
Oracle_SQL,Datatypes,varchar2|number,No Predecessor,5,80,Common/Statement/Pre
Oracle_SQL,Add_hours,to_date(*),No Predecessor,5,80,Common/Statement/Pre
Oracle_SQL,If_else,if|else,Procedure_handling,5,80,Procedure/Pre
Oracle_SQL,To_date,to_date(*),No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Into_from,into*from,No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Trunc,*trunc(%)*,No Predecessor,10,80,Common/Statement/Pre
Oracle_SQL,Connect_by_level,*connect by*,No Predecessor,10,50,Common/Statement/Pre
Oracle_SQL,Default_handling,default,No Predecessor,60,75,Default_Constraint/Pre
Oracle_SQL,Sequence_handling,sequence,No Predecessor,70,75,Sequence/Pre
Oracle_SQL,Listagg,listagg,No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Length,Length,No Predecessor,5,80,Common/Statement/Pre
Oracle_SQL,Lpad,lpad,No Predecessor,5,80,Common/Statement/Pre
Oracle_SQL,Last_day,last_day,No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Datatype,Null,No Predecessor,30,60,Datatype/Pre
Oracle_SQL,From_dual,dual,Into_from,60,80,Common/Statement/Pre
Oracle_SQL,Nvl2,nvl2,No Predecessor,50,80,Common/Statement/Pre
Oracle_SQL,Index,create unique index|create index,No Predecessor,20,60,Index/Pre
Oracle_SQL,Synonym,create or replace synonym|create synonym,No Predecessor,25,60,Synonym/Pre
Oracle_SQL,Types_handling,TYPE,No Predecessor,60,80,Type/Pre
Oracle_SQL,Systimestamp,SYSTIMESTAMP,No Predecessor,5,80,Common/Statement/Pre
Oracle_SQL,Materialized_view,Materialized|view,No Predecessor,25,80,Materialized_View/Pre
Oracle_SQL,Primarykey,alter table,No Predecessor,8,80,Primary_Key/Pre
Oracle_SQL,Rowid,rowid,No Predecessor,5,80,Common/Statement/Pre
Oracle_SQL,Conditions,"if|else|elisif,end if",No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Notnullconstraint,alter table,No Predecessor,5,80,Not_Null_Constraint/Pre
Oracle_SQL,View,create or replace,No Predecessor,25,80,View/Pre
Oracle_SQL,Instr,*instr*,No Predecessor,5,100,Common/Statement/Pre
Oracle_SQL,Add_months,add_months,No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Datatypes,bfile|binary_float|binary_double|blob|date|timestamp|nchar|raw|urowid|number|clob|varchar2|date|timestamp|char|sysdate,No Predecessor,1,100,Default_Constraint/Pre
Oracle_SQL,Sql_rowcount,rowcount,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,Currval,CURRVAL,No Predecessor,1,60,Common/Statement/Pre
Oracle_SQL,Existsnode,existsnode,No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Convert_dbms_output_to_print,SQLCODE|SQLERRM,No Predecessor,2,70,Common/Statement/Pre
Oracle_SQL,Format_error_backtrace,dbms_output,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,Merge,Merge,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,Exception,Exception,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,Connect_by_prior,start with,No Predecessor,15,50,Common/Statement/Pre
Oracle_SQL,Xmlsequence,xmlsequence,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,To_number,to_number,No Predecessor,5,70,Common/Statement/Pre
Oracle_SQL,Procedure_handling,procedure,No Predecessor,60,70,Procedure/Pre
Oracle_SQL,Limit,limit,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,To_date_char,to_date&to_char,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,Trunc,*trunc(%)*,No Predecessor,10,80,Common/Pre
Oracle_SQL,Pipe_operator,create,No Predecessor,1,60,Common/Pre
Oracle_SQL,Is_to_as,is|as,No Predecessor,10,60,Procedure/Post
Oracle_SQL,Length,Length,No Predecessor,10,80,Procedure/Statement/Pre
Oracle_SQL,Add_months,add_months,No Predecessor,5,70,Common/Pre
Oracle_SQL,Decode,*decode*,No Predecessor,5,80,Common/Pre
Oracle_SQL,Cast_to_xml,xml,Extractxml,10,70,Common/Pre
Oracle_SQL,Exception_handling,exception,No Predecessor,10,50,Procedure/Post
Oracle_SQL,Nvl,*nvl*,No Predecessor,2,100,Common/Pre
Oracle_SQL,Delete_alias,delete,No Predecessor,20,80,Common/Pre
Oracle_SQL,Current_date,current_date,No Predecessor,10,70,Common/Pre
Oracle_SQL,Balance_begin_end,begin,Exception_handling,10,80,Procedure/Post
Oracle_SQL,Comment_sys_refcursor,sys_refcursor,No Predecessor,1,70,Common/Statement/Pre
Oracle_SQL,Fix_parameter_list,sys_refcursor,Comment_sys_refcursor,10,70,Procedure/Pre
Oracle_SQL,Trim_with_ltrim_rtrim,trim,No Predecessor,10,65,Common/Pre
Oracle_SQL,Next_val,NEXTVAL,No Predecessor,10,80,Common/Pre
Oracle_SQL,Substr,substr,No Predecessor,10,70,Common/Pre
Oracle_SQL,Update_alias,Update&set,No Predecessor,5,80,Common/Pre
Oracle_SQL,Convert_replace,replace,No Predecessor,10,75,Common/Pre
Oracle_SQL,Comment_sys_refcursor,sys_refcursor,No Predecessor,1,70,Procedure/Pre
Oracle_SQL,Savepoint,savepoint|rollback,No Predecessor,10,60,Common/Pre
Oracle_SQL,To_char,to_char,No Predecessor,5,80,Common/Pre
Oracle_SQL,Opencursorfor,open,Comment_sys_refcursor,1,70,Procedure/Pre
Oracle_SQL,Xmlsequence,xmlsequence,No Predecessor,10,60,Common/Pre
Oracle_SQL,Cursor_is,cursor,No Predecessor,10,75,Common/Pre
Oracle_SQL,Extractxml,extract|extractvalue,Xmlsequence,10,60,Common/Pre
Oracle_SQL,Rownum,rownum,Into_from,10,40,Common/Statement/Pre
Oracle_SQL,Order_by,order*by,Rownum,10,40,Common/Statement/Pre
Oracle_SQL,Default_to_equals,default,No Predecessor,1,50,Procedure/Post
Oracle_SQL,Comment_proc_params_if_all_commented,sys_refcursor,Comment_sys_refcursor,10,40,Procedure/Pre
Oracle_SQL,Into_from,into*from,No Predecessor,5,70,Common/Post
