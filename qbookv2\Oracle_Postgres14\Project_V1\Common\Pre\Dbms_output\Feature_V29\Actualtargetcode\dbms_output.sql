create or replace function fn_dbmsoutput
  
DECLARE
   lines dbms_output.chararr;
   num_lines number;
BEGIN
   -- enable the buffer with default size 20000
   dbms_output.enable;
   
    raise notice 'Hello Reader!' ;
    raise notice 'Hope you have enjoyed the tutorials!' ;
    raise notice 'Have a great time exploring pl/sql!' ;
 
   num_lines := 3;
 
   dbms_output.get_lines(lines, num_lines);
 
   FOR i IN 1..num_lines LOOP
      dbms_output.put_line(lines(i));
   END LOOP;
END;