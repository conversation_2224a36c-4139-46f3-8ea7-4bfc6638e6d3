
CREATE OR REPLACE  FUNCTION "WARDS"."F_GET_CONSULTANT_REGID" (IN_EMPID IN HR.EMPLOYEE_AUXILIARY_DETAILS.EMP_ID%TYPE
) return varchar2 is
 
  V_REG_ID varchar2(50);
  V_REGXML CLOB;
begin
 
  BEGIN
    SELECT EAD.REGISTRATION_DETAILS INTO V_REGXML from HR.EMPLOYEE_AUXILIARY_DETAILS EAD
  WHERE EAD.EMP_ID=IN_EMPID;
  EXCEPTION WHEN NO_DATA_FOUND THEN
    V_REGXML := NULL;
  END;
 
  IF V_REGXML IS NOT NULL AND LENGTH(V_REGXML) > length('<RegistrationDetails/>')+1 THEN
    V_REG_ID := (select unnest(xpath('/RegistrationDetails/RegistrationData/@RegistrationNumber',xml(V_REGXML))))::text;
  END IF;
 
 
  return(V_REG_ID);
end F_GET_CONSULTANT_REGID;

