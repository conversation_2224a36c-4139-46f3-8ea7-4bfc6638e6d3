"""
Prompts for module enhancement in Stage 2 conversion analysis.
"""
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms

def create_module_enhancement_prompt(
    original_module_code: str,
    qmigrator_target_statement: str,
    ai_corrected_statement: str,
    deployment_error: str,
    ai_comparison_feedback: str = None,
    attempt_history: dict = None,
    migration_name: str = None,
    feature_name: str = None,
    keywords: str = None,
    db_terms: dict = None
) -> str:
    """
    Create a preserve-first analysis prompt for enhancing Python modules additively.

    This prompt focuses on preserving existing functionality while adding supplementary
    logic to bridge the specific transformation gap between current and expected output.

    Args:
        original_module_code: Current working Python module (DO NOT MODIFY)
        qmigrator_target_statement: Current output from existing module
        ai_corrected_statement: Expected output that we need to achieve
        deployment_error: Error context from deployment
        ai_comparison_feedback: Feedback from AI comparison (optional)
        attempt_history: Previous enhancement attempts with their outcomes (optional)
        migration_name: Migration name for database-specific context (optional)
        feature_name: Feature name from keyword mapping (optional)
        keywords: Keywords associated with this feature (optional)

    Returns:
        Formatted prompt string for additive module enhancement
    """

    # Build feedback section
    feedback_section = ""
    if ai_comparison_feedback:
        feedback_section = f"\nAI COMPARISON FEEDBACK:\n{ai_comparison_feedback}\n"

    # Build hybrid attempt history section for efficient learning
    attempt_history_section = ""
    if attempt_history and isinstance(attempt_history, dict) and (attempt_history.get('recent_attempts') or attempt_history.get('failure_patterns')):
        attempt_history_section = "\nATTEMPT HISTORY LEARNING (HYBRID APPROACH):\n"
        attempt_history_section += "=" * 70 + "\n"

        # Show all detailed attempts (no summarization)
        all_attempts = attempt_history.get('all_attempts', [])
        if all_attempts:
            attempt_history_section += f"\nALL PREVIOUS ATTEMPTS ({len(all_attempts)} total):\n"
            for attempt in all_attempts:
                attempt_num = attempt.get('attempt_number', 'Unknown')
                modules_used = attempt.get('modules_used', [])
                ai_feedback = attempt.get('ai_feedback', 'No feedback available')

                attempt_history_section += f"\nAttempt {attempt_num} - {len(modules_used)} modules:\n"

                # Safely extract module names with error handling
                module_names = []
                for m in modules_used:
                    if isinstance(m, dict):
                        module_name = m.get('module_name', 'Unknown')
                        if isinstance(module_name, str):
                            module_names.append(module_name)
                        else:
                            module_names.append(str(module_name))
                    else:
                        module_names.append(str(m))

                attempt_history_section += f"  Modules: {', '.join(module_names)}\n"
                attempt_history_section += f"  Why it failed: {ai_feedback[:200]}...\n"

        total_attempts = attempt_history.get('total_attempts', 0)
        attempt_history_section += f"\nTotal attempts analyzed: {total_attempts}\n"

        attempt_history_section += "\nSMART LEARNING INSTRUCTIONS:\n"
        attempt_history_section += "- ANALYZE what supplementary approaches were tried and why they failed\n"
        attempt_history_section += "- IDENTIFY patterns in failed additive logic attempts\n"
        attempt_history_section += "- TRY fundamentally different supplementary transformation strategies\n"
        attempt_history_section += "- AVOID repeating the same additive logic patterns that already failed\n"
        attempt_history_section += "- FOCUS on different gap-bridging approaches not yet attempted\n"
        attempt_history_section += "- LEARN from failed supplementary logic to design better additive solutions\n"
        attempt_history_section += "=" * 70 + "\n"

    # Build feature context section
    feature_context_section = ""
    if feature_name or keywords or migration_name:
        feature_context_section = f"""
FEATURE CONTEXT FROM KEYWORD MAPPING:
====================================
"""
        if migration_name:
            feature_context_section += f"Migration: {migration_name}\n"
        if feature_name:
            feature_context_section += f"Feature Name: {feature_name}\n"
        if keywords:
            feature_context_section += f"Associated Keywords: {keywords}\n"
        feature_context_section += "\n"

    # Use provided database terms or get from migration_name
    if db_terms is None:
        if not migration_name:
            raise ValueError("migration_name is required for dynamic database terms")
        db_terms = get_database_specific_terms(migration_name)

    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    migration_direction = db_terms['migration_direction']
    expert_title = db_terms['expert_title']

    prompt = f"""
You are an expert Python developer and {expert_title} specializing in SQL feature transformation.

MAIN GOAL: Transform current target output → AI corrected output
- WITHOUT losing old functionality
- WITHOUT impacting existing logic
- JUST ADD what's needed to achieve AI output

ENHANCEMENT APPROACH:
====================

CURRENT FEATURE MODULE (ANALYZE CAREFULLY):
{original_module_code}

TRANSFORMATION REQUIREMENT:
==========================

CURRENT TARGET OUTPUT (what your module produces now):
{qmigrator_target_statement}

AI CORRECTED OUTPUT (what you MUST transform the current output TO):
{ai_corrected_statement}

TRANSFORMATION DIRECTION:
========================
Your task: Modify the module to transform CURRENT OUTPUT → AI CORRECTED OUTPUT

CRITICAL: The module currently produces the CURRENT OUTPUT (which is incorrect).
You must make it produce the AI CORRECTED OUTPUT (which is correct) instead.

MIXED CONVERSION STATE CONTEXT:
==============================
The current target output may contain MIXED conversion states:
- Some SQL constructs already converted to target database ({target_db})
- Some SQL constructs still in source database format ({source_db})
- Your module must handle BOTH states and transform to final AI corrected format
- Focus on transforming current target output → AI corrected output

DEPLOYMENT ERROR CONTEXT:
{deployment_error}

{feedback_section}
{attempt_history_section}
{feature_context_section}

FEATURE ANALYSIS METHODOLOGY:
=============================

1. FEATURE IDENTIFICATION:
   - ANALYZE the module code to identify what SQL feature it handles (e.g., XML functions, JOIN operations, function conversions, etc.)
   - UNDERSTAND the current transformation patterns used in the module
   - RECOGNIZE what variations are already supported by existing logic
   - EXAMINE the existing regex patterns and transformation logic

2. MIXED STATE OUTPUT GAP ANALYSIS:
   - ANALYZE current target output (may contain mixed {source_db}/{target_db} syntax)
   - IDENTIFY which parts are already converted vs still need conversion
   - COMPARE current target output vs AI corrected output for THIS SPECIFIC FEATURE
   - FOCUS on transforming current target output → AI corrected output
   - HANDLE both source {source_db} syntax AND partially converted {target_db} syntax
   - DETERMINE what additional patterns are needed for mixed conversion states
   - EXAMINE structural differences: clause ordering, syntax positioning, element arrangement

3. FEATURE ENHANCEMENT STRATEGY:
   - PRESERVE all existing feature transformation logic
   - ADD supplementary logic to handle the missing feature variation
   - ENSURE new patterns work for similar variations of the same feature
   - MAINTAIN backward compatibility for existing feature scenarios
   - ADDRESS structural differences: element positioning, clause ordering, syntax arrangement

4. ENHANCED MODULE CONSTRUCTION:
   - PRESERVE all existing module code and functionality completely
   - ADD supplementary transformation function after existing logic
   - ENSURE final output matches AI expected output format exactly
   - MAINTAIN backward compatibility and existing functionality

5. VALIDATION APPROACH:
   - VERIFY enhanced module produces exact AI expected output
   - ENSURE existing functionality remains intact
   - VALIDATE that supplementary logic handles the gap correctly
   - CONFIRM backward compatibility with all scenarios

SUPPLEMENTARY LOGIC GUIDELINES:
===============================

ENHANCEMENT APPROACH:
- KEEP all existing functionality exactly as-is
- ADD minimal logic to transform current output → AI corrected output
- Address structural differences: element order, positioning, arrangement

PATTERN CREATION GUIDANCE:
- ANALYZE what structure the current output has (the incorrect one)
- ANALYZE what structure the AI corrected output has (the correct one)
- CREATE regex patterns that MATCH the current incorrect structure
- CREATE replacement patterns that PRODUCE the correct structure
- AVOID creating patterns that assume the correct structure already exists

PYTHON CODING REQUIREMENTS:
===========================

REGEX PATTERN SAFETY:
- ALWAYS validate regex patterns before using them
- NEVER use backreferences (\1, \2) without corresponding capturing groups ()
- USE raw strings r"pattern" for all regex patterns
- TEST regex patterns mentally before implementation
- ESCAPE special characters properly with re.escape() when needed

VALID REGEX EXAMPLES:
- Capturing group: r'(pattern)' with replacement r'\\1'
- Non-capturing group: r'(?:pattern)'
- Safe replacement: r'new_pattern' (no backreferences)
- Escaped literals: re.escape(literal_string)
- Case-insensitive: re.sub(pattern, replacement, text, flags=re.IGNORECASE)

INVALID REGEX PATTERNS (AVOID):
- r'pattern' with replacement r'\\1' (no capturing group)
- Unescaped special characters in patterns
- Malformed group references
- Missing re.IGNORECASE flag for case-insensitive matching
- Not handling optional whitespace: use \\s* or \\s+ appropriately

COMMON MISTAKES TO AVOID:
- Using \\1 without capturing group: r'pattern' -> r'\\1' ❌
- Correct approach: r'(pattern)' -> r'\\1' ✅
- Forgetting to escape special characters like . + * ? [ ] ( ) {{}} | \\
- Not accounting for optional whitespace between SQL keywords
- Missing flags for case-insensitive and multiline matching

ADDITIVE ENHANCEMENT PRINCIPLES:
- Preserve existing regex patterns and logic
- Keep all existing code unchanged
- Add new logic after existing logic
- Apply new transformations only if existing logic doesn't transform
- Maintain backward compatibility

IMPLEMENTATION GUIDELINES:
- Copy all existing code exactly as written
- Add new logic after existing code
- Add new patterns that work for the current failing case
- Ensure the module actually transforms the input (not "No transformation applied")
- Focus on achieving the target output format

FEATURE-SPECIFIC TRANSFORMATION RULES:
=====================================

UNIVERSAL PATTERN MATCHING PRINCIPLES:
=====================================

FORMATTING-AGNOSTIC APPROACH:
- DESIGN patterns that work regardless of whitespace variations
- HANDLE all possible spacing: spaces, tabs, newlines, mixed combinations
- SUPPORT all case variations: uppercase, lowercase, mixed case
- ACCOUNT for optional elements and different ordering
- PROCESS nested structures with varying complexity levels

ROBUST REGEX CONSTRUCTION GUIDELINES:
- USE flexible whitespace patterns (\\s+ for required, \\s* for optional)
- APPLY case-insensitive matching for all SQL keywords
- IMPLEMENT balanced parentheses matching for nested structures
- HANDLE quoted identifiers and string literals appropriately
- SUPPORT schema-qualified names and aliases

ADAPTIVE PATTERN STRATEGY:
- ANALYZE the feature keywords to understand what constructs to match
- BUILD patterns that capture the essential structure, not specific formatting
- CREATE flexible capturing groups for transformation components
- ENSURE patterns work across different SQL dialects and styles
- VALIDATE patterns against multiple formatting variations

CONTEXT-INDEPENDENT TRANSFORMATION STRATEGY:
- SEARCH for target constructs ANYWHERE in the statement (not just specific contexts)
- AVOID assumptions about statement structure (SELECT vs FOR vs INSERT vs UPDATE)
- FOCUS on the specific SQL construct mentioned in feature keywords
- TRANSFORM the construct regardless of its surrounding context
- PRESERVE all surrounding code while transforming only the target construct

UNIVERSAL PATTERN MATCHING APPROACH:
- USE global search patterns that work across all SQL statement types
- AVOID context-specific assumptions (like "must be in SELECT" or "must end with ;")
- IMPLEMENT construct-specific matching based on feature keywords only
- HANDLE nested constructs within any SQL statement structure
- ENSURE patterns work in FOR loops, subqueries, CTEs, functions, etc.

UNIVERSAL TRANSFORMATION METHODOLOGY:
===================================

UNIVERSAL ENHANCEMENT METHODOLOGY:
- ANALYZE feature keywords to identify the EXACT SQL construct to transform
- IGNORE the current module's context assumptions (SELECT-only, etc.)
- DESIGN patterns that find the construct ANYWHERE in any SQL statement
- CREATE transformations that work regardless of statement type or structure
- IMPLEMENT global search-and-replace logic for maximum compatibility

CONTEXT-AGNOSTIC IMPLEMENTATION PRINCIPLES:
- NEVER assume the target construct appears in a specific SQL clause
- ALWAYS use global pattern matching across the entire statement
- FOCUS only on the specific construct mentioned in feature keywords
- TRANSFORM the construct while preserving all surrounding SQL code
- ENSURE the module works in any SQL context (loops, subqueries, functions, etc.)

STATEMENT-AGNOSTIC IMPLEMENTATION STRATEGY:
- BUILD transformation logic that works in ANY SQL statement context
- CREATE global patterns that find constructs regardless of location
- IMPLEMENT direct construct transformation without context dependencies
- ENSURE transformations work in FOR loops, SELECT, INSERT, UPDATE, functions, etc.

UNIVERSAL MODULE ARCHITECTURE:
```python
def feature_function(data, schema):
    # WRONG: Context-specific approach
    # select_patterns = re.findall(r'SELECT.*?;', data)  # Only works for SELECT statements

    # CORRECT: Global construct-specific approach
    # Find the target construct ANYWHERE in the statement
    target_pattern = r'TARGET_CONSTRUCT_PATTERN'  # Based on feature keywords

    # Transform ALL occurrences regardless of context
    transformed_data = re.sub(target_pattern, replacement_function, data, flags=re.IGNORECASE | re.DOTALL)

    return transformed_data
```

CONTEXT-INDEPENDENT MODULE DESIGN:
- REPLACE context-dependent logic with global construct matching
- IMPLEMENT direct pattern matching based on feature keywords only
- REMOVE assumptions about statement structure or SQL clause location
- CREATE transformations that work in any SQL statement type or context

COMMON CONTEXT DEPENDENCY MISTAKES TO AVOID:
- Searching only within SELECT statements when construct can appear anywhere
- Assuming statements end with semicolons when they might be part of larger constructs
- Looking for constructs in specific SQL clauses instead of globally
- Requiring specific statement patterns instead of focusing on the target construct
- Making assumptions about surrounding SQL context

UNIVERSAL COMPATIBILITY VALIDATION:
- TEST enhanced module against ALL SQL statement types (SELECT, FOR, INSERT, UPDATE, etc.)
- VERIFY the module finds and transforms the target construct regardless of context
- ENSURE transformations work in nested structures, subqueries, and complex statements
- CONFIRM the module produces exact AI expected output in any SQL context

CONTEXT-INDEPENDENCE VERIFICATION:
- VALIDATE that the module works in FOR loops, not just SELECT statements
- ENSURE patterns match constructs in any SQL clause or statement position
- CONFIRM transformations preserve surrounding SQL code correctly
- VERIFY the module handles the target construct universally across all scenarios

SAFE REGEX IMPLEMENTATION:
- Use proper capturing groups when backreferences are needed
- Validate all regex patterns for syntax correctness
- Handle edge cases and malformed input gracefully
- Test patterns mentally before implementation

OUTPUT REQUIREMENTS:
===================

Provide a JSON response with PRESERVE-FIRST enhanced module:

FEATURE-CENTRIC ANALYSIS APPROACH:
- UNDERSTAND what specific feature this module handles and how it works
- IDENTIFY what variations of this feature are missing or incomplete
- ANALYZE the gap between current feature handling and required feature coverage
- DESIGN supplementary logic that enhances the feature to handle ALL variations
- ENSURE the enhanced module handles the current case AND similar feature scenarios

ENHANCED FEATURE MODULE ARCHITECTURE:
- PRESERVE all existing module code and feature handling logic
- ADD supplementary transformation logic that extends feature coverage
- ENSURE the enhanced module handles ALL variations of the specific feature
- MAINTAIN backward compatibility for all existing feature scenarios
- VALIDATE that new feature variations are handled correctly

MIXED STATE ENHANCEMENT STRATEGY:
```python
def existing_function_name(input_statement, schema_name):
    # Step 1: Keep existing logic (handles source {source_db} syntax)
    existing_output = existing_transformation_logic(input_statement, schema_name)

    # Step 2: Handle mixed conversion states
    # Input may contain:
    # - Pure {source_db} syntax (original database constructs)
    # - Pure {target_db} syntax (converted database constructs)
    # - Mixed syntax (some converted, some not)

    # Step 3: Transform current target output → AI corrected output
    if existing_output != input_statement:
        # Existing logic applied some transformation
        current_target = existing_output
    else:
        # No transformation applied, use input as current target
        current_target = input_statement

    # Step 4: Bridge gap from current target → AI corrected format
    if needs_further_transformation(current_target):
        final_output = transform_to_ai_corrected_format(current_target)
        return final_output

    return current_target
```

SELF-VALIDATION CHECKLIST:
=========================
Before finalizing the enhanced code, mentally verify:
- Does every backreference (\\1, \\2, etc.) have a corresponding capturing group ()?
- Are all special regex characters properly escaped?
- Will patterns handle ALL formatting variations (spacing, case, nesting)?
- Does the transformation produce exact AI expected output format?
- Are re.IGNORECASE and re.DOTALL flags used appropriately?
- Does the code work regardless of input formatting style?
- Are patterns flexible enough to handle real-world SQL variations?

SUCCESS CRITERIA:
================
- Enhanced module output must match AI expected CONTENT exactly
- IGNORE formatting differences (whitespace, tabs, line breaks)
- IGNORE case differences in keywords
- IGNORE table name formatting variations
- FOCUS on logical SQL content and structure match
- If existing code already produces correct content, DO NOT change anything

CONTEXT-INDEPENDENT TRANSFORMATION PRINCIPLES:
=============================================

UNIVERSAL CONSTRUCT MATCHING:
- FOCUS only on the specific SQL construct mentioned in feature keywords
- SEARCH for the construct ANYWHERE in the SQL statement, not just specific contexts
- AVOID assumptions about statement structure (SELECT vs FOR vs INSERT vs UPDATE)
- USE global pattern matching that works across all SQL statement types
- IMPLEMENT direct construct transformation without context dependencies

ANTI-PATTERN GUIDELINES:
- DO NOT search only within SELECT statements - constructs can appear anywhere
- DO NOT assume statements end with semicolons - they might be part of larger constructs
- DO NOT look for constructs in specific SQL clauses - search globally
- DO NOT require specific statement patterns - focus only on the target construct
- DO NOT make assumptions about surrounding SQL context or structure

GLOBAL TRANSFORMATION STRATEGY:
- IDENTIFY the exact construct from feature keywords and module purpose
- CREATE patterns that match the construct regardless of its location in SQL
- IMPLEMENT transformations that work in any SQL statement context
- PRESERVE all surrounding SQL code while transforming only the target construct
- VALIDATE the transformation works universally across all SQL scenarios

ADDITIVE PATTERN DETECTION STRATEGY:
===================================

CURRENT CASE ANALYSIS:
- EXISTING PATTERNS: May be too restrictive for current input format
- MISSING DETECTION: Patterns don't match the actual SQL structure
- ADDITIVE SOLUTION: Add broader patterns that catch missed cases

ADDITIVE PATTERN APPROACH:
```python
def enhanced_function(data, schema):
    original_data = data

    # PRESERVE: Keep all existing logic exactly as-is
    # ... existing code unchanged ...

    # ADD: Supplementary detection for missed cases
    if data == original_data:  # Existing logic didn't transform
        # Add broader patterns to catch the current case
        broader_pattern = r'BROADER_PATTERN_FOR_CURRENT_CASE'
        if re.search(broader_pattern, data, re.IGNORECASE | re.DOTALL):
            # Apply transformation to achieve AI corrected output
            transformed_data = apply_transformation_for_ai_output(data)
            return transformed_data

    return data
```

PATTERN ENHANCEMENT GUIDELINES:
- ADD patterns that work in FOR loops, not just SELECT statements
- ADD patterns that don't require semicolon endings
- ADD patterns that find constructs ANYWHERE in the statement
- PRESERVE all existing patterns and logic

COMMENT MARKER PRESERVATION:
- IGNORE and PRESERVE comment markers like "comment_quad_marker_0_us", "comment_quad_marker_1_us", etc.
- DO NOT transform or modify any text matching pattern: comment_quad_marker_[digit]_us
- THESE are comment masking placeholders that must remain unchanged
- ENSURE transformations work around these markers without affecting them
- PRESERVE the exact format and position of all comment markers

PATTERN VALIDATION FUNDAMENTALS:
- VALIDATE all regex patterns for syntactic correctness
- ENSURE capturing groups match backreference usage
- IMPLEMENT flexible whitespace and case handling
- CREATE patterns that adapt to different formatting styles
- TEST patterns against various input format scenarios

ROBUST IMPLEMENTATION STRATEGY:
- BUILD transformation logic that is format-independent
- DESIGN patterns that capture essential SQL structure
- IMPLEMENT comprehensive matching for target constructs
- ENSURE transformations work across all input variations
- VALIDATE output consistency regardless of input formatting

WHITESPACE HANDLING:
- Use \\s* for optional whitespace (zero or more)
- Use \\s+ for required whitespace (one or more)
- Account for tabs, spaces, and newlines in SQL

EFFICIENCY RULES:
================
- DO NOT repeat existing code that already works
- ONLY add logic where there's an actual content gap
- If existing module already handles the case correctly, return as-is
- ADD minimal code necessary to bridge the content gap

MAIN GOAL:
==========
Transform CURRENT TARGET OUTPUT → AI CORRECTED OUTPUT exactly

ANALYSIS APPROACH:
=================
1. EXAMINE the current target output carefully - this shows what the module produces NOW
2. EXAMINE the AI corrected output carefully - this shows what the module SHOULD produce
3. IDENTIFY the specific differences between what IS and what SHOULD BE
4. CREATE transformation logic that converts from what IS to what SHOULD BE

METHODOLOGY:
1. Compare current vs AI corrected output structure
2. Identify differences in element positioning/ordering
3. **EXAMINE the actual data structure carefully** - look for intermediate content between elements
4. Create patterns that detect what the module CURRENTLY produces (the incorrect structure)
5. Design replacements that produce what the module SHOULD produce (the correct structure)
6. **PRESERVE any intermediate content** while reordering the main elements
7. Keep all existing functionality working

TRANSFORMATION LOGIC FOCUS:
- DETECT the incorrect structure that currently exists in the output
- REPLACE it with the correct structure that should exist in the output
- DO NOT create patterns that assume the correct structure already exists
- DO NOT create patterns that try to "maintain" or "ensure" the correct structure
- CREATE patterns that CONVERT from incorrect to correct structure

PATTERN FLEXIBILITY REQUIREMENTS:
- HANDLE intermediate content between SQL elements (comments, markers, whitespace)
- USE capturing groups to preserve important intermediate content
- ACCOUNT for multi-line structures and varying formatting
- ENSURE patterns work with different spacing and line break variations
- DESIGN robust patterns that match real-world SQL formatting variations

CRITICAL UNDERSTANDING - CONVERT vs ENSURE:
==========================================

WRONG APPROACH (ENSURE):
- Creating patterns that assume the correct structure already exists
- Adding logic to "ensure" or "maintain" the correct structure
- Looking for the desired pattern and trying to preserve it

CORRECT APPROACH (CONVERT):
- Creating patterns that detect the incorrect structure that currently exists
- Adding logic to "convert" or "transform" the incorrect structure to correct structure
- Looking for the current problematic pattern and transforming it to the desired pattern
- **EXAMINING the actual data structure** to understand what elements exist between components
- **CREATING flexible patterns** that can handle intermediate content while reordering main elements

SUCCESS CRITERIA:
- Current target output becomes AI corrected output
- All existing functionality preserved
- Module shows "Statement transformed" not "No transformation applied"
- Minimal change to achieve maximum result

CODE CHANGE ASSESSMENT:
- Set code_changed to TRUE if you actually modified/added logic to the module
- Set code_changed to FALSE if you just reproduced the exact same code without any changes
- Be honest about whether you enhanced the module or just copied the original logic

{{
  "enhanced_code": "Complete enhanced Python module that transforms current target output to AI corrected output while preserving all existing functionality",
  "analysis": "Simple explanation of: 1) What existing functionality was kept unchanged, 2) What minimal logic was added to transform current target output to AI corrected output, 3) How this achieves the exact AI corrected output, 4) Confirmation that no existing functionality was broken",
  "code_changed": true_if_you_actually_added_logic_to_transform_current_to_ai_output_false_if_no_changes_made
}}
"""

    return prompt
