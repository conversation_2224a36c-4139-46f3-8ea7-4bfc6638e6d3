create table "patientbilldetails" 
   (	"patientservicerequistid" number not null enable, 
	"servicename" varchar2(1000), 
	"serviceid" number, 
	"individualrate" number, 
	"createdby" varchar2(50), 
	"createddate" date, 
	"updatedby" varchar2(50), 
	"updateddate" date, 
	"billno" varchar2(100), 
	"servicequantity" number, 
	"incomeamount" number, 
	"servicetypeid" number, 
	"servicerequesteddate" date, 
	"bedcategoryid" number, 
	"deptid" varchar2(50), 
	"doctoramount" number, 
	"doctorid" number, 
	"individualratefx" number, 
	"extrachargeflag" varchar2(5), 
	"patientoccupancy" char(1), 
	"startdatetime" date, 
	"enddatetime" date, 
	"creditdiscountpercentage" number, 
	"supplimantarybillno" varchar2(100), 
	"reftariff" number, 
	"remarks" varchar2(350), 
	"servicerequestdetailsid" number, 
	"requestid" number, 
	"patientpayable" char(1), 
	"bedsideflag" varchar2(5), 
	"bedcode" varchar2(100), 
	"reftarifffx" number, 
	"status" number, 
	"transferlocationid" varchar2(100), 
	"basederivedrelation" number, 
	"requestno_new" varchar2(200), 
	"gst_flag" char(1), 
	"cgst_tariff" number, 
	"sgst_tariff" number, 
	"cgst_percent" number, 
	"sgst_percent" number, 
	"total_gst_percent" number, 
	"discountamount" number, 
	"discountpercent" number, 
	"amountafter_discount" number, 
	 constraint "patientservicerequest_pk" primary key ("patientservicerequistid")
  using index  enable, 
	 constraint "patientbilldetails_fk" foreign key ("billno")
	  references "patientbill" ("billno") on delete cascade enable
   ) 
  partition by range (patientservicerequistid);
create table patientbilldetails_3 partition of  patientbilldetails  for values from (1) to (3000000);
create table patientbilldetails_4 partition of  patientbilldetails  for values from (3000000) to (3100000);
create table patientbilldetails_5 partition of  patientbilldetails  for values from (3100000) to (9000000);
create table patientbilldetails_6 partition of  patientbilldetails  for values from (9000000) to (12000000);
create table patientbilldetails_7 partition of  patientbilldetails  for values from (12000000) to (15000000);
create table sys_p61 partition of  patientbilldetails  for values from (15000000) to (16000000);
create table sys_p101 partition of  patientbilldetails  for values from (16000000) to (17000000);
create table sys_p121 partition of  patientbilldetails  for values from (17000000) to (18000000);
create table sys_p141 partition of  patientbilldetails  for values from (18000000) to (19000000);
create table sys_p161 partition of  patientbilldetails  for values from (19000000) to (20000000);
create table sys_p181 partition of  patientbilldetails  for values from (20000000) to (21000000);
create table sys_p221 partition of  patientbilldetails  for values from (21000000) to (22000000);
create table sys_p241 partition of  patientbilldetails  for values from (22000000) to (23000000);
create table sys_p261 partition of  patientbilldetails  for values from (23000000) to (24000000);
create table sys_p281 partition of  patientbilldetails  for values from (24000000) to (25000000);
create table sys_p301 partition of  patientbilldetails  for values from (25000000) to (26000000);
create table sys_p341 partition of  patientbilldetails  for values from (26000000) to (27000000);
create table sys_p361 partition of  patientbilldetails  for values from (27000000) to (28000000);
create table sys_p381 partition of  patientbilldetails  for values from (28000000) to (29000000);
create table sys_p401 partition of  patientbilldetails  for values from (29000000) to (30000000);
create table sys_p421 partition of  patientbilldetails  for values from (30000000) to (31000000);
create table sys_p461 partition of  patientbilldetails  for values from (31000000) to (32000000);
create table sys_p601 partition of  patientbilldetails  for values from (32000000) to (33000000);
create table sys_p824 partition of  patientbilldetails  for values from (33000000) to (34000000);
create table sys_p1061 partition of  patientbilldetails  for values from (34000000) to (35000000);
create table sys_p1301 partition of  patientbilldetails  for values from (35000000) to (36000000);
create table sys_p1621 partition of  patientbilldetails  for values from (36000000) to (37000000);
create table sys_p2001 partition of  patientbilldetails  for values from (37000000) to (38000000);
create table sys_p2361 partition of  patientbilldetails  for values from (38000000) to (39000000);
create table sys_p2721 partition of  patientbilldetails  for values from (39000000) to (40000000);
create table sys_p3081 partition of  patientbilldetails  for values from (40000000) to (41000000);
create table sys_p3481 partition of  patientbilldetails  for values from (41000000) to (42000000);
create table sys_p3841 partition of  patientbilldetails  for values from (42000000) to (43000000);
create table sys_p4241 partition of  patientbilldetails  for values from (43000000) to (44000000);
create table sys_p4621 partition of  patientbilldetails  for values from (44000000) to (45000000);


create table sales_by_region (item# integer, qty integer, 
             store_name varchar(30), state_code varchar(2),
             sale_date date) 
     PARTITION BY LIST (STATE_CODE) 
CREATE TABLE REGION_EAST
       PARTITION OF  SALES_BY_REGION FOR VALUES IN ('MA','NY','CT','NH','ME','MD','VA','PA','NJ');
CREATE TABLE REGION_WEST
       PARTITION OF  SALES_BY_REGION FOR VALUES IN ('CA','AZ','NM','OR','WA','UT','NV','CO');
CREATE TABLE REGION_SOUTH
       PARTITION OF  SALES_BY_REGION FOR VALUES IN ('TX','KY','TN','LA','MS','AR','AL','GA');
CREATE TABLE REGION_CENTRAL 
       PARTITION OF  SALES_BY_REGION FOR VALUES IN ('OH','ND','SD','MO','IL','MI','IA');
CREATE TABLE REGION_NULL
       PARTITION OF  SALES_BY_REGION FOR VALUES IN (NULL);
CREATE TABLE REGION_UNKNOWN
       PARTITION OF  SALES_BY_REGION FOR VALUES IN (DEFAULT);