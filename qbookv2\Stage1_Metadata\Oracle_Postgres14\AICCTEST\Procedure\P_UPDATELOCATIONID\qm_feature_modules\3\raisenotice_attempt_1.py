import re


str_data = """
FOR REC IN C1 LOOP

        RAISE NOTICE EXECUTE 'UPDATE % SET % = % WHERE % = %;', REC.TABLE_NAME, REC.COLUMN_NAME, IN_TOVAL, REC.COLUMN_NAME, IN_FROMVAL;
"""
 
def raisenotice(data, schema):
    space_str = re.sub(r' +', ' ', data)
    space_str_split = space_str.split('\n')
    for i in space_str_split:
        if 'raise notice' in i:
            space_str_replace = re.sub(r'%', '% ', i, re.DOTALL)
            space_str = space_str.replace(i, space_str_replace)

        else:
            space_str = space_str
    data = space_str
    data = re.sub(r'\braise\s*notice\s*execute\b', 'RAISE NOTICE ', data, flags=re.DOTALL | re.I)

    # # Additional transformation to remove 'EXECUTE' after 'RAISE NOTICE'
    # data = re.sub(r'RAISE NOTICE\s+EXECUTE\s+', 'RAISE NOTICE ', data, flags=re.DOTALL | re.I)

    return data

print(raisenotice(str_data,'BB'))